# Azure OpenAI Integration Setup

This guide explains how to configure the CV Scoring API to use Azure OpenAI Service instead of the regular OpenAI API.

## Overview

The application now supports both:
- **Regular OpenAI API** (api.openai.com)
- **Azure OpenAI Service** (your-resource.openai.azure.com)

Azure OpenAI provides the same models as OpenAI but with additional enterprise features like private networking, regional availability, and enhanced security.

## Prerequisites

1. **Azure Subscription** with access to Azure OpenAI Service
2. **Azure OpenAI Resource** created in Azure Portal
3. **Model Deployment** (e.g., GPT-4) in your Azure OpenAI resource

## Step 1: Create Azure OpenAI Resource

1. Go to [Azure Portal](https://portal.azure.com)
2. Create a new **Azure OpenAI** resource
3. Choose your subscription, resource group, and region
4. Wait for deployment to complete

## Step 2: Deploy a Model

1. Go to your Azure OpenAI resource
2. Navigate to **Model deployments**
3. Click **Create new deployment**
4. Choose **gpt-4** (or your preferred model)
5. Give it a deployment name (e.g., "gpt-4")
6. Deploy the model

## Step 3: Get Configuration Details

From your Azure OpenAI resource, collect:

### API Key
1. Go to **Keys and Endpoint**
2. Copy **Key 1** or **Key 2**

### Endpoint URL
1. From **Keys and Endpoint**
2. Copy the **Endpoint** URL (e.g., `https://your-resource.openai.azure.com/`)

### Deployment Name
1. From **Model deployments**
2. Note the **Deployment name** you created (e.g., "gpt-4")

## Step 4: Configure Environment Variables

Set the following environment variables:

```bash
# Required for Azure OpenAI
export OPENAI_API_KEY="your-azure-openai-api-key"
export OPENAI_API_TYPE="azure"
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
export AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4"

# Optional (uses default if not set)
export AZURE_OPENAI_API_VERSION="2024-02-15-preview"
```

### Example .env file:
```env
# Azure OpenAI Configuration
OPENAI_API_KEY=1234567890abcdef1234567890abcdef
OPENAI_API_TYPE=azure
AZURE_OPENAI_ENDPOINT=https://my-openai-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
AZURE_OPENAI_API_VERSION=2024-02-15-preview
```

## Step 5: Test the Configuration

### Using the Test Script
```bash
python test_azure_openai.py
```

### Expected Output
```
2024-01-15 10:30:15 - app.services.scoring_service - INFO - Azure OpenAI client initialized successfully with deployment: gpt-4
2024-01-15 10:30:15 - app.services.scoring_service - INFO - Starting candidate scoring process
2024-01-15 10:30:15 - app.services.scoring_service - DEBUG - API Type: azure
2024-01-15 10:30:15 - app.services.scoring_service - DEBUG - Using model: gpt-4
✅ Azure OpenAI integration is working correctly!
```

## Configuration Options

### Required Settings

| Variable | Description | Example |
|----------|-------------|---------|
| `OPENAI_API_KEY` | Your Azure OpenAI API key | `1234567890abcdef...` |
| `OPENAI_API_TYPE` | Set to "azure" | `azure` |
| `AZURE_OPENAI_ENDPOINT` | Your Azure OpenAI endpoint | `https://my-resource.openai.azure.com/` |
| `AZURE_OPENAI_DEPLOYMENT_NAME` | Your model deployment name | `gpt-4` |

### Optional Settings

| Variable | Description | Default |
|----------|-------------|---------|
| `AZURE_OPENAI_API_VERSION` | Azure OpenAI API version | `2024-02-15-preview` |

## Switching Between OpenAI and Azure OpenAI

### To use Regular OpenAI:
```bash
export OPENAI_API_TYPE="openai"
export OPENAI_API_KEY="your-openai-api-key"
```

### To use Azure OpenAI:
```bash
export OPENAI_API_TYPE="azure"
export OPENAI_API_KEY="your-azure-openai-api-key"
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
export AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4"
```

## Troubleshooting

### Common Issues

#### 1. "OpenAI client not initialized"
**Cause:** Missing or incorrect configuration

**Solution:** Verify all required environment variables are set:
```bash
echo $OPENAI_API_KEY
echo $OPENAI_API_TYPE
echo $AZURE_OPENAI_ENDPOINT
echo $AZURE_OPENAI_DEPLOYMENT_NAME
```

#### 2. "Invalid endpoint"
**Cause:** Incorrect endpoint URL format

**Solution:** Ensure endpoint includes protocol and trailing slash:
- ✅ Correct: `https://my-resource.openai.azure.com/`
- ❌ Wrong: `my-resource.openai.azure.com`
- ❌ Wrong: `https://my-resource.openai.azure.com`

#### 3. "Deployment not found"
**Cause:** Incorrect deployment name or model not deployed

**Solution:** 
1. Check deployment name in Azure Portal
2. Ensure model is successfully deployed
3. Verify deployment name matches exactly

#### 4. "Authentication failed"
**Cause:** Invalid API key

**Solution:**
1. Regenerate API key in Azure Portal
2. Update environment variable
3. Restart application

#### 5. "Rate limit exceeded"
**Cause:** Too many requests to Azure OpenAI

**Solution:**
1. Check your Azure OpenAI quota
2. Implement request throttling
3. Consider upgrading your Azure OpenAI tier

## Benefits of Azure OpenAI

### Security
- Private networking with VNet integration
- Customer-managed keys
- Azure Active Directory authentication
- Compliance certifications (SOC 2, HIPAA, etc.)

### Reliability
- 99.9% SLA
- Regional availability
- Disaster recovery options

### Integration
- Seamless Azure ecosystem integration
- Azure Monitor logging
- Azure Key Vault for secrets

### Cost Management
- Predictable pricing
- Azure cost management tools
- Reserved capacity options

## API Differences

The application handles the differences automatically, but for reference:

### Regular OpenAI
```python
client = AsyncOpenAI(api_key="sk-...")
response = await client.chat.completions.create(model="gpt-4", ...)
```

### Azure OpenAI
```python
client = AsyncAzureOpenAI(
    api_key="...",
    api_version="2024-02-15-preview",
    azure_endpoint="https://resource.openai.azure.com/"
)
response = await client.chat.completions.create(model="deployment-name", ...)
```

## Support

For Azure OpenAI specific issues:
- [Azure OpenAI Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/)
- [Azure Support](https://azure.microsoft.com/en-us/support/)

For application-specific issues:
- Check application logs
- Run test scripts
- Review configuration settings
