#!/usr/bin/env python3
"""
Test script to demonstrate scoring debug logging functionality
"""

import asyncio
import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import setup_logging
from app.services.scoring_service import ScoringService

# Get logger for this script
logger = logging.getLogger(__name__)

async def test_scoring_debug():
    """Test the scoring service with debug logging"""
    
    # Setup logging
    setup_logging("DEBUG")
    
    # Create scoring service
    scoring_service = ScoringService()
    
    # Sample candidate data
    candidate_data = {
        "personal_info": {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        },
        "education": [
            {
                "degree": "Bachelor of Science",
                "field": "Computer Science",
                "institution": "University of Technology",
                "graduation_year": 2020
            }
        ],
        "experience": [
            {
                "title": "Software Developer",
                "company": "Tech Corp",
                "duration": "2 years",
                "description": "Developed web applications using Python and JavaScript"
            }
        ],
        "skills": [
            "Python", "JavaScript", "React", "FastAPI", "PostgreSQL"
        ]
    }
    
    # Sample job requirements
    job_requirements = {
        "title": "Senior Python Developer",
        "required_skills": ["Python", "FastAPI", "PostgreSQL"],
        "preferred_skills": ["React", "Docker", "AWS"],
        "experience_years": 3,
        "education_level": "Bachelor's degree"
    }
    
    # Sample scoring config
    scoring_config = {
        "weights": {
            "education_relevance": 0.20,
            "skills_match": 0.30,
            "experience_quality": 0.25,
            "technical_proficiency": 0.15,
            "career_progression": 0.05,
            "language_fit": 0.05
        }
    }
    
    logger.info("=" * 60)
    logger.info("TESTING SCORING SERVICE WITH DEBUG LOGGING")
    logger.info("=" * 60)
    
    try:
        # This will trigger all the debug logging we added
        result = await scoring_service.score_candidate(
            candidate_data,
            job_requirements,
            scoring_config
        )
        
        logger.info("\n" + "=" * 60)
        logger.info("SCORING COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)
        logger.info(f"Final Score: {result['final_score']}")
        logger.info(f"Confidence: {result['confidence_level']}")
        logger.info(f"Recommendation: {result['recommendation']}")

        if result.get('flags'):
            logger.info(f"Flags: {result['flags']}")

    except Exception as e:
        logger.error(f"\nERROR: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # Note: This test requires OpenAI API key to be set
    logger.info("Note: This test requires OPENAI_API_KEY to be set in your environment")
    logger.info("If not set, the scoring will fall back to default values with debug logs")
    logger.info("")
    
    asyncio.run(test_scoring_debug())
