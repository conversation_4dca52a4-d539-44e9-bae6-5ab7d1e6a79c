# Candidate Processing Status Flow

This document describes the candidate processing status flow in the CV scoring system, including the new `parsing_completed` status.

## Status Values

The system uses the following status values to track candidate processing:

| Status | Description | When Set |
|--------|-------------|----------|
| `pending` | CV uploaded but not yet sent to HRFlow | Initial state when candidate is created |
| `processing` | CV sent to HRFlow for parsing | When CV is sent to HRFlow API |
| `parsing_completed` | CV parsed and structured data available | When `parsed_data` field is populated |
| `completed` | CV parsed and scored | After scoring is completed |
| `failed` | Processing failed | When any step in the process fails |

## Status Flow Diagrams

### Synchronous Processing (with auto-scoring enabled)
```
pending → processing → parsing_completed → completed
   ↓           ↓             ↓               ↓
Upload CV → Send to → Parse complete → Scoring complete
           HRFlow    (parsed_data     (scoring_results
                     populated)       populated)
```

### Synchronous Processing (auto-scoring disabled)
```
pending → processing → parsing_completed
   ↓           ↓             ↓
Upload CV → Send to → Parse complete
           HRFlow    (parsed_data
                     populated)
```

### Asynchronous Processing (webhook-based)
```
pending → processing → ... (webhook) ... → parsing_completed → completed
   ↓           ↓                                ↓               ↓
Upload CV → Send to                    → Parse complete → Scoring complete
           HRFlow                      (webhook updates  (if auto-scoring
           (async)                     parsed_data)      enabled)
```

## Key Changes

### Before
- Status went directly from `processing` to `completed` when parsing was done
- No distinction between parsing completion and scoring completion

### After
- New `parsing_completed` status indicates when CV parsing is done and `parsed_data` is available
- `completed` status now specifically indicates that both parsing and scoring are done
- Better granularity for tracking processing progress

## Implementation Details

### Constants
All status values are defined in `app/constants/candidate_status.py`:

```python
class CandidateStatus:
    PENDING = "pending"
    PROCESSING = "processing"
    PARSING_COMPLETED = "parsing_completed"
    COMPLETED = "completed"
    FAILED = "failed"
```

### When Status Changes

1. **`pending` → `processing`**: When candidate is created and CV is sent to HRFlow
2. **`processing` → `parsing_completed`**: When `parsed_data` field is populated (either sync or webhook)
3. **`parsing_completed` → `completed`**: When scoring is completed (if auto-scoring is enabled)
4. **Any status → `failed`**: When an error occurs during processing

### Code Locations

The status is updated in the following locations:

1. **Candidate Service** (`app/services/candidate_service.py`):
   - Sets `processing` when CV is uploaded
   - Sets `parsing_completed` when parsed_data is populated
   - Sets `completed` when scoring is done
   - Sets `failed` on errors

2. **Webhook Service** (`app/services/webhook_service.py`):
   - Sets `parsing_completed` when webhook updates parsed_data

## Database Migration

Run the migration script to update existing data:

```sql
-- Update existing candidates with parsed_data but no scoring to parsing_completed
UPDATE candidates 
SET processing_status = 'parsing_completed'
WHERE processing_status = 'completed' 
  AND parsed_data IS NOT NULL 
  AND (scoring_results IS NULL OR final_score IS NULL);
```

## API Impact

### Frontend Considerations

The frontend should be updated to handle the new status:

1. **Progress Indicators**: Show parsing vs scoring progress separately
2. **Status Display**: Update status labels to include "Parsing Complete"
3. **Actions**: Allow manual scoring trigger for candidates in `parsing_completed` status

### Backward Compatibility

- Existing API endpoints continue to work
- Status values are additive (no breaking changes)
- Old status values remain valid

## Benefits

1. **Better Visibility**: Clear distinction between parsing and scoring completion
2. **Improved UX**: Users can see when parsing is done even if scoring is pending
3. **Debugging**: Easier to identify where processing stopped
4. **Flexibility**: Allows for manual scoring of parsed candidates
5. **Monitoring**: Better metrics on parsing vs scoring performance

## Helper Methods

The `CandidateStatus` class provides utility methods:

```python
# Check if status is valid
CandidateStatus.is_valid_status("parsing_completed")  # True

# Check if processing is complete (parsing done)
CandidateStatus.is_processing_complete("parsing_completed")  # True

# Check if status is final (no more processing)
CandidateStatus.is_final_status("completed")  # True
```
