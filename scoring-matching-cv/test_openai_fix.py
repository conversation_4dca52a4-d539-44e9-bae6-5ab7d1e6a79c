#!/usr/bin/env python3
"""
Test script to verify OpenAI API v1.0+ integration is working correctly
"""

import asyncio
import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import setup_logging
from app.services.scoring_service import ScoringService

# Get logger for this script
logger = logging.getLogger(__name__)

async def test_openai_integration():
    """Test the OpenAI API integration with the new v1.0+ syntax"""
    
    # Setup logging
    setup_logging("DEBUG")
    
    logger.info("=" * 60)
    logger.info("TESTING OPENAI API v1.0+ INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Create scoring service
        scoring_service = ScoringService()
        
        # Sample candidate data
        candidate_data = {
            "personal_info": {
                "name": "<PERSON>",
                "email": "<EMAIL>",
                "phone": "+1234567890"
            },
            "education": [
                {
                    "degree": "Master of Science",
                    "field": "Computer Science",
                    "institution": "Stanford University",
                    "graduation_year": 2022
                }
            ],
            "experience": [
                {
                    "title": "Senior Software Engineer",
                    "company": "Google",
                    "duration": "3 years",
                    "description": "Led development of machine learning systems using Python and TensorFlow"
                }
            ],
            "skills": [
                "Python", "Machine Learning", "TensorFlow", "FastAPI", "PostgreSQL", "Docker"
            ]
        }
        
        # Sample job requirements
        job_requirements = {
            "title": "Senior AI Engineer",
            "required_skills": ["Python", "Machine Learning", "TensorFlow"],
            "preferred_skills": ["FastAPI", "Docker", "AWS", "Kubernetes"],
            "experience_years": 3,
            "education_level": "Master's degree"
        }
        
        # Sample scoring config
        scoring_config = {
            "weights": {
                "education_relevance": 0.20,
                "skills_match": 0.35,
                "experience_quality": 0.25,
                "technical_proficiency": 0.15,
                "career_progression": 0.03,
                "language_fit": 0.02
            }
        }
        
        logger.info("Starting scoring process with OpenAI API v1.0+...")
        
        # This will test the new OpenAI API integration
        result = await scoring_service.score_candidate(
            candidate_data,
            job_requirements,
            scoring_config
        )
        
        logger.info("\n" + "=" * 60)
        logger.info("OPENAI API INTEGRATION TEST SUCCESSFUL")
        logger.info("=" * 60)
        logger.info(f"Final Score: {result['final_score']}")
        logger.info(f"Confidence: {result['confidence_level']}")
        logger.info(f"Recommendation: {result['recommendation']}")
        
        if result.get('flags'):
            logger.info(f"Flags: {result['flags']}")
            
        # Test individual scoring dimensions
        if result.get('scores'):
            logger.info("\nDetailed Scores:")
            for dimension, data in result['scores'].items():
                logger.info(f"  {dimension}: {data['score']}/100 - {data['reasoning'][:100]}...")
                
        logger.info("\n✅ OpenAI API v1.0+ integration is working correctly!")
        
    except Exception as e:
        logger.error(f"\n❌ ERROR: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Check if it's an API key issue
        if "API key" in str(e) or "not configured" in str(e):
            logger.warning("\n💡 SOLUTION: Make sure to set your OPENAI_API_KEY environment variable")
            logger.warning("   export OPENAI_API_KEY='your-api-key-here'")
        elif "APIRemovedInV1" in str(e):
            logger.error("\n🔧 This error should be fixed now with the v1.0+ API integration")
        else:
            logger.error("\n🔍 Check the logs above for more details about the error")

if __name__ == "__main__":
    logger.info("Testing OpenAI API v1.0+ Integration...")
    logger.info("This test verifies that the OpenAI API calls work with the new syntax")
    logger.info("")
    
    asyncio.run(test_openai_integration())
