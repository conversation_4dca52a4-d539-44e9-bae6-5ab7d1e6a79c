from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.project import Project, ProjectCreate, ProjectUpdate
from app.schemas.candidate import CandidateList
from app.services.project_service import ProjectService
from app.services.candidate_service import CandidateService

router = APIRouter()


@router.post("/", response_model=Project, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new hiring project
    """
    project_service = ProjectService(db)
    project = project_service.create_project(project_data, current_user.id)
    return project


@router.get("/", response_model=List[Project])
async def list_projects(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List all projects for the current user
    """
    project_service = ProjectService(db)
    projects = project_service.get_user_projects(current_user.id, skip=skip, limit=limit)
    return projects


@router.get("/{project_id}", response_model=Project)
async def get_project(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get project details
    """
    project_service = ProjectService(db)
    project = project_service.get_project(project_id, current_user.id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    return project


@router.put("/{project_id}", response_model=Project)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update project details
    """
    project_service = ProjectService(db)
    project = project_service.update_project(project_id, project_update, current_user.id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    return project


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Archive a project
    """
    project_service = ProjectService(db)
    success = project_service.archive_project(project_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )


@router.post("/{project_id}/candidates")
async def upload_cvs(
    project_id: int,
    files: List[UploadFile] = File(...),
    auto_score: bool = Form(True),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload CVs to a project
    """
    candidate_service = CandidateService(db)
    result = await candidate_service.upload_cvs(project_id, files, current_user.id, auto_score)
    return result


@router.get("/{project_id}/candidates", response_model=List[CandidateList])
async def list_candidates(
    project_id: int,
    sort_by: str = "final_score",
    order: str = "desc",
    selected_only: bool = False,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List candidates for a project
    """
    candidate_service = CandidateService(db)
    candidates = candidate_service.get_project_candidates(
        project_id, current_user.id, sort_by, order, selected_only
    )
    return candidates


@router.post("/{project_id}/score-all")
async def score_all_candidates(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Score all candidates in a project
    """
    candidate_service = CandidateService(db)
    result = await candidate_service.score_all_candidates(project_id, current_user.id)
    return result


@router.get("/{project_id}/selected-candidates")
async def export_selected_candidates(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export selected candidates for contact
    """
    candidate_service = CandidateService(db)
    result = candidate_service.export_selected_candidates(project_id, current_user.id)
    return result
