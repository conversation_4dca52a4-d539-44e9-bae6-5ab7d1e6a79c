"""
Constants for candidate processing status values
"""

class CandidateStatus:
    """Candidate processing status constants"""
    PENDING = "pending"
    PROCESSING = "processing"
    PARSING_COMPLETED = "parsing_completed"
    COMPLETED = "completed"
    FAILED = "failed"
    
    @classmethod
    def all_statuses(cls):
        """Get all available status values"""
        return [
            cls.PENDING,
            cls.PROCESSING,
            cls.PARSING_COMPLETED,
            cls.COMPLETED,
            cls.FAILED
        ]
    
    @classmethod
    def is_valid_status(cls, status: str) -> bool:
        """Check if a status value is valid"""
        return status in cls.all_statuses()
    
    @classmethod
    def is_processing_complete(cls, status: str) -> bool:
        """Check if processing is complete (parsing_completed or completed)"""
        return status in [cls.PARSING_COMPLETED, cls.COMPLETED]
    
    @classmethod
    def is_final_status(cls, status: str) -> bool:
        """Check if status is final (completed or failed)"""
        return status in [cls.COMPLETED, cls.FAILED]
