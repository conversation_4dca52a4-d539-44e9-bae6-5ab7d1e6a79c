from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "postgresql://username:password@localhost:5432/cv_scoring_db"
    
    # JWT
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # HRFlow API
    HRFLOW_API_KEY: str = ""
    HRFLOW_API_URL: str = "https://api.hrflow.ai/v1"
    HRFLOW_SOURCE_KEY: str = ""
    HRFLOW_USER_EMAIL: str = "7185260f34f12fa081277a0a71f55cccc6397fc8"
    HRFLOW_WEBHOOK_SECRET: str = "wsk_d87a7123b589588441e1d65ad1637362"
    HRFLOW_SYNC_PARSING: bool = True  # True for real-time parsing, False for async webhook-based
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = ""
    OPENAI_API_TYPE: str = "openai"  # "openai" or "azure"

    # Azure OpenAI Configuration (only needed if OPENAI_API_TYPE is "azure")
    AZURE_OPENAI_ENDPOINT: str = ""  # e.g., "https://your-resource.openai.azure.com/"
    AZURE_OPENAI_API_VERSION: str = "2024-02-15-preview"  # API version
    AZURE_OPENAI_DEPLOYMENT_NAME: str = ""  # Your deployment name (e.g., "gpt-4")
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # File Upload
    MAX_FILE_SIZE_MB: int = 10
    ALLOWED_FILE_TYPES: str = "pdf,doc,docx"
    UPLOAD_DIR: str = "./uploads"
    
    # Security
    RATE_LIMIT_UPLOADS_PER_HOUR: int = 50
    ENABLE_PROMPT_INJECTION_DETECTION: bool = True
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    @property
    def allowed_file_extensions(self) -> List[str]:
        return self.ALLOWED_FILE_TYPES.split(",")
    
    @property
    def max_file_size_bytes(self) -> int:
        return self.MAX_FILE_SIZE_MB * 1024 * 1024
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
