import logging
import sys
from typing import Dict, Any

def setup_logging(log_level: str = "DEBUG") -> None:
    """
    Setup logging configuration for the application
    """
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers for our services
    service_loggers = [
        'app.services.scoring_service',
        'app.services.candidate_service',
        'app.services.hrflow_service',
        'app.services.webhook_service',
        'app.services.file_service',
        'app.services.project_service',
        'app.services.user_service',
        'app.services.security_service',
        'app.api.v1.endpoints.webhooks',
        'app.main'
    ]
    
    for logger_name in service_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
        # Don't add handlers here as they inherit from root logger
    
    # Reduce noise from external libraries
    external_loggers = [
        'httpx',
        'urllib3',
        'sqlalchemy.engine',
        'sqlalchemy.pool',
        'openai'
    ]
    
    for logger_name in external_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name
    """
    return logging.getLogger(name)
