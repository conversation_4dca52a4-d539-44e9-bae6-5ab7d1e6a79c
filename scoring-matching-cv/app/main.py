from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import logging

from app.core.config import settings
from app.core.database import engine, Base
from app.core.logging_config import setup_logging
from app.api.v1.router import api_router
from app.middleware.rate_limiting import RateLimitMiddleware
from app.middleware.validation import ValidationMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    setup_logging("DEBUG" if settings.DEBUG else "INFO")
    logger = logging.getLogger(__name__)
    logger.info("Starting up CV Scoring API...")
    yield
    # Shutdown
    logger.info("Shutting down CV Scoring API...")


app = FastAPI(
    title="CV Scoring API",
    description="AI-powered CV scoring and matching system",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# Security middleware
# app.add_middleware(
#     TrustedHostMiddleware,
#     allowed_hosts=["localhost", "127.0.0.1", "*"]
# )

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Security middleware
app.add_middleware(ValidationMiddleware)
app.add_middleware(RateLimitMiddleware, calls_per_hour=settings.RATE_LIMIT_UPLOADS_PER_HOUR)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    return {
        "message": "CV Scoring API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
