from pydantic import BaseModel, validator
from typing import Dict, Any, Optional, Union
from datetime import datetime
import json
import urllib.parse


class HRFlowWebhookPayload(BaseModel):
    """Schema for HRFlow webhook payload"""
    type: str
    origin: str
    message: str
    profile: Union[str, Dict[str, Any]]  # Can be JSON string or parsed dict
    
    @validator('profile', pre=True)
    def parse_profile(cls, v):
        """Parse the profile JSON string"""
        if isinstance(v, str):
            # Handle empty or minimal profile data
            if not v or v == "{}":
                return {}
            try:
                # URL decode first, then parse JSON
                decoded = urllib.parse.unquote_plus(v)
                parsed = json.loads(decoded)
                print(f"Successfully parsed profile: {parsed}")
                return parsed
            except (json.JSONDecodeError, ValueError) as e:
                print(f"Failed to parse profile JSON: {e}")
                print(f"Raw value: {v}")
                print(f"Decoded value: {urllib.parse.unquote_plus(v) if v else 'None'}")
                # Return empty dict if parsing fails
                return {}
        return v or {}


class WebhookEventCreate(BaseModel):
    """Schema for creating webhook events"""
    event_type: str
    origin: str
    message: str
    profile_key: Optional[str] = None
    source_key: Optional[str] = None
    raw_payload: Dict[str, Any]


class WebhookEventResponse(BaseModel):
    """Schema for webhook event response"""
    id: int
    event_type: str
    origin: str
    message: str
    profile_key: Optional[str]
    source_key: Optional[str]
    processed: bool
    processing_error: Optional[str]
    received_at: datetime
    processed_at: Optional[datetime]
    
    class Config:
        from_attributes = True
