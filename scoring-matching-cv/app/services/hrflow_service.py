import json
import logging
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from hrflow import <PERSON><PERSON><PERSON>

from app.core.config import settings

# Configure logger for HRFlow service
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class HRFlowService:
    def __init__(self):
        self.api_key = settings.HRFLOW_API_KEY
        self.base_url = settings.HRFLOW_API_URL

        # Initialize HRFlow SDK client
        if self.api_key:
            # Debug: Check what environment variables we're getting
            logger.debug(f"HRFLOW_USER_EMAIL from settings: '{settings.HRFLOW_USER_EMAIL}'")
            logger.debug(f"HRFLOW_SOURCE_KEY from settings: '{settings.HRFLOW_SOURCE_KEY}'")

            # Use a valid email address for HRFlow SDK initialization
            user_email = settings.HRFLOW_USER_EMAIL or "<EMAIL>"
            logger.debug(f"Final user_email to use: '{user_email}'")

            self.client = Hrflow(
                api_secret=self.api_key,
                api_user=user_email
            )
            logger.info(f"HRFlow SDK initialized with user: {user_email}")
        else:
            self.client = None

        # Keep headers for direct API calls if needed
        self.headers = {
            "X-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }

    async def parse_cv(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Parse CV using HRFlow SDK with webhook support
        """
        if not self.api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HRFlow API key not configured"
            )

        # Get source key from environment configuration
        if not settings.HRFLOW_SOURCE_KEY:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HRFlow source key not configured"
            )

        if not self.client:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HRFlow client not initialized"
            )

        try:
            # Use HRFlow SDK to parse CV
            # Configure parsing mode based on settings
            sync_parsing = 1 if settings.HRFLOW_SYNC_PARSING else 0
            webhook_parsing_sending = 0 if settings.HRFLOW_SYNC_PARSING else 1

            logger.info(f"HRFlow parsing mode: {'sync' if settings.HRFLOW_SYNC_PARSING else 'async'}")
            logger.debug(f"sync_parsing={sync_parsing}, webhook_parsing_sending={webhook_parsing_sending}")

            try:
                logger.debug(f"Attempting HRFlow SDK call with profile_file parameter...")
                response = self.client.profile.parsing.add_file(
                    source_key=settings.HRFLOW_SOURCE_KEY,
                    profile_file=file_content,
                    sync_parsing=sync_parsing,
                    webhook_parsing_sending=webhook_parsing_sending
                )
                logger.debug(f"First attempt successful. Response: {response}")
            except TypeError as te:
                logger.warning(f"First attempt failed: {te}")
                # Try with different parameter names
                try:
                    logger.debug(f"Attempting HRFlow SDK call with file_binary parameter...")
                    response = self.client.profile.parsing.add_file(
                        source_key=settings.HRFLOW_SOURCE_KEY,
                        file_binary=file_content,
                        sync_parsing=sync_parsing,
                        webhook_parsing_sending=webhook_parsing_sending
                    )
                    logger.debug(f"Second attempt successful. Response: {response}")
                except TypeError as te2:
                    logger.warning(f"Second attempt failed: {te2}")
                    # Fall back to minimal parameters
                    logger.debug(f"Attempting HRFlow SDK call with minimal parameters...")
                    response = self.client.profile.parsing.add_file(
                        source_key=settings.HRFLOW_SOURCE_KEY,
                        file_binary=file_content
                    )
                    logger.debug(f"Third attempt successful. Response: {response}")

            logger.debug(f"Final HRFlow response: {response}")
            logger.debug(f"Response type: {type(response)}")

            if not response:
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Invalid response from HRFlow API"
                )

            # Check for HRFlow API errors
            if response.get("code") and response.get("code") not in [200, 201, 202]:
                error_message = response.get("message", "Unknown HRFlow API error")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=f"HRFlow API error (code {response.get('code')}): {error_message}"
                )

            # Handle sync processing (real-time parsing) - code 201
            if response.get("code") == 201:
                logger.info("Sync processing completed - profile parsed immediately")
                return {
                    "status": "completed",
                    "message": response.get("message", "Profile parsed successfully"),
                    "webhook_enabled": False,
                    "data": response.get("data", {}),
                    "profile_key": response.get("data", {}).get("profile", {}).get("key") if response.get("data") else None
                }

            # Handle async processing (webhook enabled) - code 202
            if response.get("code") == 202:
                logger.info("Async processing initiated - profile will be processed via webhook")
                return {
                    "status": "processing",
                    "message": response.get("message", "Profile sent to parsing queue"),
                    "webhook_enabled": True,
                    "profile_key": response.get("data", {}).get("key") if response.get("data") else None
                }

            # For legacy sync processing (code 200)
            if response.get("code") == 200 and response.get("data"):
                return {
                    "status": "completed",
                    "message": response.get("message", "Profile processed successfully"),
                    "webhook_enabled": False,
                    "data": response.get("data", {}),
                    "profile_key": response.get("data", {}).get("key") if response.get("data") else None
                }

            # If we get here, the response format is unexpected
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Unexpected HRFlow API response format: {response}"
            )

        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"HRFlow API error: {str(e)}"
            )

    def extract_structured_data(self, hrflow_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and structure candidate data from HRFlow response
        """
        try:
            # Handle different response formats
            data = hrflow_response.get("data", {})

            # For sync parsing (real-time), profile data is nested under data.profile
            if isinstance(data, dict) and "profile" in data:
                profile_data = data["profile"]
                logger.debug("Extracting data from sync parsing response (data.profile)")
            # For legacy format or direct profile data
            else:
                profile_data = data
                logger.debug("Extracting data from legacy/direct format")
            
            # Extract personal information
            personal_info = {
                "name": profile_data.get("info", {}).get("full_name"),
                "email": profile_data.get("info", {}).get("email"),
                "phone": profile_data.get("info", {}).get("phone"),
                "location": profile_data.get("info", {}).get("location", {}).get("text")
            }
            
            # Extract education
            education = []
            for edu in profile_data.get("educations", []):
                education.append({
                    "degree": edu.get("title"),
                    "field": edu.get("description"),
                    "institution": edu.get("school"),
                    "start_date": edu.get("date_start"),
                    "end_date": edu.get("date_end")
                })
            
            # Extract experience
            experience = []
            for exp in profile_data.get("experiences", []):
                experience.append({
                    "title": exp.get("title"),
                    "company": exp.get("company"),
                    "duration": f"{exp.get('date_start', '')} - {exp.get('date_end', '')}",
                    "description": exp.get("description"),
                    "skills_used": [skill.get("name") for skill in exp.get("skills", [])]
                })
            
            # Extract skills
            skills = [skill.get("name") for skill in profile_data.get("skills", [])]
            
            # Extract languages
            languages = [lang.get("name") for lang in profile_data.get("languages", [])]
            
            # Extract certifications (if available)
            certifications = []
            for cert in profile_data.get("certifications", []):
                certifications.append(cert.get("name"))
            
            return {
                "personal_info": personal_info,
                "education": education,
                "experience": experience,
                "skills": skills,
                "languages": languages,
                "certifications": certifications
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing HRFlow response: {str(e)}"
            )
