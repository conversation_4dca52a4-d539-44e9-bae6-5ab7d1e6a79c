# Debug Logging for CV Scoring System

This document explains the comprehensive debug logging that has been added to the CV scoring functionality.

## Overview

Debug logging has been added to provide detailed insights into the scoring process, making it easier to troubleshoot issues and understand how candidates are being evaluated.

## Logging Configuration

### Setup
The logging is configured in `app/core/logging_config.py` and automatically initialized when the application starts.

### Log Levels
- **DEBUG**: Detailed information for debugging
- **INFO**: General information about the process flow
- **WARNING**: Warning messages for non-critical issues
- **ERROR**: Error messages for failures

### Environment Control
- In development (`DEBUG=true`): Logs at DEBUG level
- In production (`DEBUG=false`): Logs at INFO level

## Scoring Service Logging

### ScoringService (`app/services/scoring_service.py`)

The scoring service now includes comprehensive logging for:

#### Main Scoring Process (`score_candidate`)
- Input validation and sanitization
- Prompt creation details
- OpenAI API call status and response
- Response parsing and validation
- Final score calculation
- Error handling and fallback scenarios

#### OpenAI API Calls (`_call_openai_api`)
- API request parameters (model, tokens, temperature)
- Request/response timing
- Usage statistics
- Error details

#### Response Parsing (`_parse_scoring_response`)
- Raw response content
- JSON extraction process
- Validation of each scoring dimension
- Score validation (0-100 range)

#### Score Calculation (`_calculate_final_score`)
- Weight configuration
- Individual dimension contributions
- Final weighted score breakdown

## Candidate Service Logging

### CandidateService (`app/services/candidate_service.py`)

#### Individual Candidate Scoring (`_score_candidate`)
- Candidate and project information
- Job requirements validation
- Scoring service interaction
- Result processing and storage
- Error handling with fallback scores

#### Bulk Scoring (`score_all_candidates`)
- Project verification
- Candidate discovery and filtering
- Progress tracking for multiple candidates
- Success/failure statistics

#### Auto-scoring During Upload
- Auto-scoring enablement status
- Integration with CV processing pipeline

## Log Output Examples

### Successful Scoring
```
2024-01-15 10:30:15 - app.services.candidate_service - INFO - Starting scoring process for candidate 123
2024-01-15 10:30:15 - app.services.candidate_service - DEBUG - Candidate filename: john_doe_resume.pdf
2024-01-15 10:30:15 - app.services.candidate_service - DEBUG - Fetching project 456 for scoring...
2024-01-15 10:30:15 - app.services.candidate_service - DEBUG - Project found: Senior Developer Position
2024-01-15 10:30:15 - app.services.scoring_service - INFO - Starting candidate scoring process
2024-01-15 10:30:15 - app.services.scoring_service - DEBUG - Candidate data keys: ['personal_info', 'education', 'experience', 'skills']
2024-01-15 10:30:15 - app.services.scoring_service - DEBUG - Creating scoring prompt...
2024-01-15 10:30:15 - app.services.scoring_service - INFO - Calling OpenAI API for scoring...
2024-01-15 10:30:18 - app.services.scoring_service - DEBUG - OpenAI API call successful
2024-01-15 10:30:18 - app.services.scoring_service - DEBUG - Final calculated score: 78.5
2024-01-15 10:30:18 - app.services.candidate_service - INFO - Candidate 123 scored successfully: 78.5 (confidence: High)
```

### Error Scenario
```
2024-01-15 10:35:20 - app.services.scoring_service - ERROR - OpenAI API call failed: Invalid API key
2024-01-15 10:35:20 - app.services.scoring_service - WARNING - Falling back to default scoring due to error
2024-01-15 10:35:20 - app.services.candidate_service - WARNING - Applying fallback score for candidate 124
```

## Testing Debug Logging

### Test Script
Use the provided test script to see debug logging in action:

```bash
python test_scoring_debug.py
```

### API Testing
When testing through the API, debug logs will appear in the application console/logs.

### Key Endpoints for Testing
- `POST /api/v1/projects/{project_id}/candidates/upload` (with auto_score=true)
- `POST /api/v1/projects/{project_id}/candidates/score-all`
- `GET /api/v1/candidates/{candidate_id}` (to see scoring results)

## Troubleshooting with Logs

### Common Issues and Log Patterns

#### OpenAI API Issues
Look for:
- "OpenAI API call failed" messages
- API key validation errors
- Rate limiting messages
- Timeout errors

#### Data Validation Issues
Look for:
- "Missing dimension" errors
- "Invalid score" warnings
- "No parsed candidate data" errors

#### Configuration Issues
Look for:
- "Job requirements not configured" errors
- "Project not found" errors
- Weight configuration warnings

### Log Analysis Tips

1. **Follow the Flow**: Logs are structured to follow the scoring process chronologically
2. **Check Candidate IDs**: Each log includes candidate ID for tracking
3. **Monitor Score Breakdown**: Debug logs show how final scores are calculated
4. **Watch for Fallbacks**: Warning messages indicate when fallback scoring is used

## Performance Monitoring

The debug logs also help monitor performance:
- OpenAI API response times
- Bulk scoring progress
- Error rates and patterns
- Fallback usage frequency

## Production Considerations

In production:
- Set `DEBUG=false` to reduce log verbosity
- Monitor ERROR and WARNING level logs
- Set up log aggregation for analysis
- Consider log rotation for disk space management

## Configuration

### Environment Variables
- `DEBUG`: Controls log level (true/false)
- `OPENAI_API_KEY`: Required for AI scoring (fallback if missing)

### Logging Levels by Service
- `app.services.scoring_service`: DEBUG
- `app.services.candidate_service`: DEBUG
- External libraries (httpx, sqlalchemy): WARNING (to reduce noise)
