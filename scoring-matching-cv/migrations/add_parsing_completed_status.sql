-- Migration to add parsing_completed status support
-- This migration ensures existing data is compatible with the new status flow

-- Update any existing candidates that have parsed_data but are still marked as "completed"
-- to use the new "parsing_completed" status if they haven't been scored yet
UPDATE candidates 
SET processing_status = 'parsing_completed'
WHERE processing_status = 'completed' 
  AND parsed_data IS NOT NULL 
  AND (scoring_results IS NULL OR final_score IS NULL);

-- Add a comment to document the status values
COMMENT ON COLUMN candidates.processing_status IS 'Processing status: pending, processing, parsing_completed, completed, failed';

-- Optional: Add a check constraint to ensure only valid status values are used
-- Uncomment the following lines if you want to enforce status values at the database level
-- ALTER TABLE candidates 
-- ADD CONSTRAINT check_processing_status 
-- CHECK (processing_status IN ('pending', 'processing', 'parsing_completed', 'completed', 'failed'));

-- Create an index on processing_status for better query performance
CREATE INDEX IF NOT EXISTS idx_candidates_processing_status ON candidates(processing_status);

-- Create an index on parsed_data existence for queries that check if parsing is complete
CREATE INDEX IF NOT EXISTS idx_candidates_parsed_data_exists ON candidates((parsed_data IS NOT NULL));

-- Show the current status distribution
SELECT 
    processing_status,
    COUNT(*) as count,
    COUNT(CASE WHEN parsed_data IS NOT NULL THEN 1 END) as with_parsed_data,
    COUNT(CASE WHEN scoring_results IS NOT NULL THEN 1 END) as with_scoring
FROM candidates 
GROUP BY processing_status 
ORDER BY processing_status;
