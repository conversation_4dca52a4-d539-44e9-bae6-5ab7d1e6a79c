#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI integration is working correctly
"""

import asyncio
import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.logging_config import setup_logging
from app.services.scoring_service import ScoringService
from app.core.config import settings

# Get logger for this script
logger = logging.getLogger(__name__)

async def test_azure_openai_integration():
    """Test the Azure OpenAI integration"""
    
    # Setup logging
    setup_logging("DEBUG")
    
    logger.info("=" * 60)
    logger.info("TESTING AZURE OPENAI INTEGRATION")
    logger.info("=" * 60)
    
    # Display current configuration
    logger.info(f"API Type: {settings.OPENAI_API_TYPE}")
    logger.info(f"API Key configured: {'Yes' if settings.OPENAI_API_KEY else 'No'}")
    
    if settings.OPENAI_API_TYPE.lower() == "azure":
        logger.info(f"Azure Endpoint: {settings.AZURE_OPENAI_ENDPOINT}")
        logger.info(f"Azure API Version: {settings.AZURE_OPENAI_API_VERSION}")
        logger.info(f"Azure Deployment: {settings.AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    try:
        # Create scoring service
        scoring_service = ScoringService()
        
        # Check if client was initialized properly
        if not scoring_service.openai_client:
            logger.error("❌ OpenAI client not initialized!")
            logger.error("Please check your configuration:")
            logger.error("1. Set OPENAI_API_KEY")
            logger.error("2. Set OPENAI_API_TYPE=azure")
            logger.error("3. Set AZURE_OPENAI_ENDPOINT")
            logger.error("4. Set AZURE_OPENAI_DEPLOYMENT_NAME")
            return
        
        # Sample candidate data
        candidate_data = {
            "personal_info": {
                "name": "Alex Johnson",
                "email": "<EMAIL>",
                "phone": "+1234567890"
            },
            "education": [
                {
                    "degree": "Bachelor of Science",
                    "field": "Software Engineering",
                    "institution": "MIT",
                    "graduation_year": 2021
                }
            ],
            "experience": [
                {
                    "title": "Full Stack Developer",
                    "company": "Microsoft",
                    "duration": "2 years",
                    "description": "Developed cloud applications using Azure services and .NET"
                }
            ],
            "skills": [
                "C#", ".NET", "Azure", "React", "SQL Server", "Docker"
            ]
        }
        
        # Sample job requirements
        job_requirements = {
            "title": "Senior Full Stack Developer",
            "required_skills": ["C#", ".NET", "Azure"],
            "preferred_skills": ["React", "Docker", "Kubernetes", "DevOps"],
            "experience_years": 2,
            "education_level": "Bachelor's degree"
        }
        
        # Sample scoring config
        scoring_config = {
            "weights": {
                "education_relevance": 0.15,
                "skills_match": 0.40,
                "experience_quality": 0.25,
                "technical_proficiency": 0.15,
                "career_progression": 0.03,
                "language_fit": 0.02
            }
        }
        
        logger.info("Starting scoring process with Azure OpenAI...")
        
        # This will test the Azure OpenAI integration
        result = await scoring_service.score_candidate(
            candidate_data,
            job_requirements,
            scoring_config
        )
        
        logger.info("\n" + "=" * 60)
        logger.info("AZURE OPENAI INTEGRATION TEST SUCCESSFUL")
        logger.info("=" * 60)
        logger.info(f"Final Score: {result['final_score']}")
        logger.info(f"Confidence: {result['confidence_level']}")
        logger.info(f"Recommendation: {result['recommendation']}")
        
        if result.get('flags'):
            logger.info(f"Flags: {result['flags']}")
            
        # Test individual scoring dimensions
        if result.get('scores'):
            logger.info("\nDetailed Scores:")
            for dimension, data in result['scores'].items():
                logger.info(f"  {dimension}: {data['score']}/100")
                logger.info(f"    Reasoning: {data['reasoning'][:150]}...")
                
        logger.info("\n✅ Azure OpenAI integration is working correctly!")
        
    except Exception as e:
        logger.error(f"\n❌ ERROR: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Provide specific troubleshooting for Azure OpenAI
        if "API key" in str(e) or "not configured" in str(e):
            logger.warning("\n💡 AZURE OPENAI CONFIGURATION:")
            logger.warning("   Set the following environment variables:")
            logger.warning("   export OPENAI_API_KEY='your-azure-openai-key'")
            logger.warning("   export OPENAI_API_TYPE='azure'")
            logger.warning("   export AZURE_OPENAI_ENDPOINT='https://your-resource.openai.azure.com/'")
            logger.warning("   export AZURE_OPENAI_DEPLOYMENT_NAME='your-deployment-name'")
        elif "endpoint" in str(e).lower():
            logger.error("\n🔧 Check your Azure OpenAI endpoint URL")
        elif "deployment" in str(e).lower():
            logger.error("\n🔧 Check your Azure OpenAI deployment name")
        else:
            logger.error("\n🔍 Check the logs above for more details about the error")

def display_configuration_help():
    """Display help for Azure OpenAI configuration"""
    logger.info("=" * 60)
    logger.info("AZURE OPENAI CONFIGURATION GUIDE")
    logger.info("=" * 60)
    logger.info("")
    logger.info("1. Get your Azure OpenAI credentials from Azure Portal:")
    logger.info("   - Go to your Azure OpenAI resource")
    logger.info("   - Copy the API Key from 'Keys and Endpoint'")
    logger.info("   - Copy the Endpoint URL")
    logger.info("   - Note your deployment name")
    logger.info("")
    logger.info("2. Set environment variables:")
    logger.info("   export OPENAI_API_KEY='your-azure-api-key'")
    logger.info("   export OPENAI_API_TYPE='azure'")
    logger.info("   export AZURE_OPENAI_ENDPOINT='https://your-resource.openai.azure.com/'")
    logger.info("   export AZURE_OPENAI_DEPLOYMENT_NAME='gpt-4'  # or your deployment name")
    logger.info("")
    logger.info("3. Optional: Set API version (default is 2024-02-15-preview)")
    logger.info("   export AZURE_OPENAI_API_VERSION='2024-02-15-preview'")
    logger.info("")

if __name__ == "__main__":
    logger.info("Testing Azure OpenAI Integration...")
    logger.info("This test verifies that Azure OpenAI API calls work correctly")
    logger.info("")
    
    # Check if this looks like Azure configuration
    if settings.OPENAI_API_TYPE.lower() != "azure":
        logger.warning("⚠️  OPENAI_API_TYPE is not set to 'azure'")
        logger.warning("   This test is designed for Azure OpenAI")
        logger.warning("   Set OPENAI_API_TYPE=azure to test Azure OpenAI")
        logger.info("")
        display_configuration_help()
    else:
        asyncio.run(test_azure_openai_integration())
