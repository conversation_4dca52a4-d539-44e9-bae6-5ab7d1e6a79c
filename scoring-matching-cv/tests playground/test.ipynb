{"cells": [{"cell_type": "code", "execution_count": 5, "id": "554144f5", "metadata": {}, "outputs": [], "source": ["with open('test.txt', 'r') as file:\n", "    test_txt = file.read()"]}, {"cell_type": "code", "execution_count": 2, "id": "e0ffee17", "metadata": {}, "outputs": [{"data": {"text/plain": ["'type=profile.parsing.success&origin=api&message=Profile+parsing+succeed&profile%5Bsource_key%5D=0892ef1f91ea52860edb1fdf8cefd5234a42aa72&profile%5Bid%5D=52850290&profile%5Bkey%5D=413c041ce188942dac5d749e4ed7df70c44e5519&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Bparsing%5D=0&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Brevealing%5D=0&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Bembedding%5D=0&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Bsearching%5D=0&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Bscoring%5D=0&profile%5Bconsent_algorithmic%5D%5Bowner%5D%5Breasoning%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Bparsing%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Brevealing%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Bembedding%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Bsearching%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Bscoring%5D=0&profile%5Bconsent_algorithmic%5D%5Bcontroller%5D%5Breasoning%5D=0&profile%5Bsource%5D%5Bkey%5D=0892ef1f91ea52860edb1fdf8cefd5234a42aa72&profile%5Bupdated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Bcreated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Binfo%5D%5Bfull_name%5D=AYMAN+FRIMANE&profile%5Binfo%5D%5Bfirst_name%5D=AYMAN&profile%5Binfo%5D%5Blast_name%5D=FRIMANE&profile%5Binfo%5D%5Bemail%5D=ayman.frimane12%40gmail.com&profile%5Binfo%5D%5Bphone%5D=0623608844&profile%5Binfo%5D%5Burls%5D%5B0%5D%5Btype%5D=linkedin&profile%5Binfo%5D%5Burls%5D%5B0%5D%5Burl%5D=https%3A%2F%2Fwww.linkedin.com%2Fin%2Fayman-frimane-1601922b5&profile%5Binfo%5D%5Bpicture%5D=https%3A%2F%2Friminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com%2Fteams%2F67b512d20fed124cc5f67db9beb49dd0b91fd2e7%2Fsources%2F0892ef1f91ea52860edb1fdf8cefd5234a42aa72%2Fprofiles%2F413c041ce188942dac5d749e4ed7df70c44e5519%2Fparsing%2Fpicture.png&profile%5Binfo%5D%5Bgender%5D=male&profile%5Binfo%5D%5Bsummary%5D=A+Masters+student+in+Cyber+Security+and+Big+Data%2C+at+faculty+of+science+and+technology+Tangier%2C%0Acurrently+looking+for+valuable+work+or+study+experience+to+further+enhance+my+own+competence%2C+to%0Afurther+may+future+career.&profile%5Btext_language%5D=en&profile%5Btext%5D=AYMAN+FRIMANE%0Aayman.frimane12%40gmail.com%0A%0A0623608844%0A%0AFquih+Ben+Salah%0A%0Ain+linkedin.com%2Fin%2Fayman-frimane-1601922b5%0A%0ASUMMARY%0A%0AA+Masters+student+in+Cyber+Security+and+Big+Data%2C+at+faculty+of+science+and+technology+Tangier%2C%0Acurrently+looking+for+valuable+work+or+study+experience+to+further+enhance+my+own+competence%2C+to%0Afurther+may+future+career.%0A%0AEXPERIENCE%0A%0A2025%0A%0AProject%3A+FinTech+Application+for+Ethereum+Transactions+and+Exchanges%0A%0A%E2%80%A2%0A%0ADeveloped+an+innovative+FinTech+platform+leveraging+Ethereum+blockchain+and%0Amicroservices+to+manage+secure%2C+decentralized+e-transactions+and+cryptocurrency%0Aexchanges+using+Solidity+smart+contracts.+Integrated+Web3+and+MetaMask+for+intuitive+user%0Ainteraction%2C+ensuring+scalability%2C+security%2C+and+performance+while+addressing+technical%0Achallenges+in+platform+development.%0AProject%3A+Unsupervised+Learning+for+Malware+Analysis%0A%0A.%0A%0ADesigning+and+implementing+an+unsupervised+learning+pipeline+for+malware+analysis%2C%0Afocusing+on+clustering+and+dimensionality+reduction%2C+with+deployment+via+FastAPI%2C+an%0AAngular+frontend%2C+MLOps+practices+using+Kubeflow%2C+and+detailed+analysis+with%0Acomprehensive+reporting.%0A%0AProject%3A+Designing+and+simulating+a+cloud+computing+environment%0A%0A.%0A%0ADesigned+and+simulated+a+Cloud+Computing+architecture+using+CloudSim%2C+evaluating%0Ascenarios+based+onspecific+requirements.+Implemented+an+OpenStack-based+laaS+with%0ALinux%2C+developed+a+SaaS+application%2C+and+created+a+Python+script+to+monitor+instance%0Aavailability+%2899.5%25+daily+uptime%29+for+an+SLA%2C+utilizing+OpenStack.%0A%0AProject%3A+Creating+a+multiple+agent+system+using+jade%0A%0A%E2%80%A2%0A%0ADeveloped+a+simulation+to+optimize+resource+allocation%2C+establishing+logic+for+each+agent%0Aand+reviewing+performance%2C+while+trying+to+optimize+the+behavior.%0A%0A2024%0A%0AStage%3A+D%C3%A9veloppement+d%27une+application+web+pour+la+gestion+et+l%27archivage+des+soutenances+de%0Ath%C3%A8ses.%0A%0A%E2%80%A2%0A%0A%E2%80%A2%0A%0ACoordinated+project+tasks%2C+ensuring+adherence+to+project+requirements+and+deadlines.%0AConducted+comprehensive+project+analyses%2C+identifying+and+rectifying+discrepancies+in+work%0Aflow+designs.%0A%0AEDUCATION%0A%0AMaster+of+Cyber+security+and+big+data%0AFST+Abdelmalek+Essa%C3%A2di+University+Tangier+Morocco%0A%0A%E2%80%A2%0A%0A%E2%80%A2%0A%0ASpecialization+in+systems+security.%0ABig+data+management+and+analytics.%0AMachine+Learning+and+Al+Algorithms.%0A%0ABachelor+of+computer+engineering%0AFST-+Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco%0AProgramming+and+Software+Development.%0A%0ASep+2024+-+ongoing%0A%0A2024%0A%0A%E2%80%A2%0A%0ANetworking+and+Distributed+Systems.%0A%0A.%0A%0AComputer+Systems+and+Architecture.%0A%0ADiploma+in+Science+and+Technology+%28MIPC%29%0A%0A2023%0A%0AFST-Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco%0A%0A2018%0A%0ABaccalaur%C3%A9at+in+Physics+and+chemistry%0ALyc%C3%A9e+El+Kindi+Fquih+Ben+Salah+Morocco%0A%0ACOMPETENCIES%0A%0A.%0A%0A%E2%80%A2%0A%0AProgramming%3A+Java%2C+Python%2C+Php%0AWeb+Technologies%3A+HTML5%2C+CSS%2C+Laravel%2C%0ABootstrap.%0A%E2%80%A2+Data+base+management%3A+SQL%2C+NoSQL%2C+Oracle%2C%0ASQL+server.%0A%E2%80%A2+Modularisation%3A+UML%2C+Merise.%0A%0A%E2%80%A2%0A%0AProject+management%3A+Agile%2C+Scrum%0AAI%2C+Machine+learning+Data+mining.%0A%0A%E2%80%A2+Team+work%3A+able+to+work+well+with+diverse%0Atypes+of+people.%0AAdaptation%3A+fast+at+adapting+to+new%0Ascenarios%2C+and+work+flows%0A%E2%80%A2+Problem+Solving%3A+fast+at+thinking+and+trying%0Anew+solutions+to+rising+problems.%0ALanguages%3A+Arabic%2C+English%2C+French%0A%0ACERTIFICATES%0A%0A.%0A%0ABig+Data+101%3A+IBM+SkillsBuild+june+2025%0A%0A%E2%8B%85%0A%0AHadoop+101%3A+IBM+SkillsBuild+june+2025%0A%0A%E2%80%A2%0A%0ASpark+Fundamentals+I%3A+IBM+Skills+Build+june+2025%0A%0AHOBBIES+AND+INTERESTS%0A%0A%E2%80%A2%0A%0AReading%3A+Books+and+light+novels.%0A%0A%E2%80%A2+Travel%3A+visiting+and+seeing+new+places+and+having+new+experiences.%0A%0A%E2%80%A2%0A%0A%E2%80%A2%0A%0ATechnologies+%3A+trying+to+keep+us+with+new+inventions%2C+especially+in+computer+tech.%0AGaming%3A+playing+video+games%2C+helps+me+unwind+and+also+i+find+purpose+in+the+challenge+of%0Aeach+unique+game.&profile%5Bexperiences_duration%5D=1&profile%5Beducations_duration%5D=3.0739726027397&profile%5Bexperiences%5D%5B0%5D%5Bkey%5D=05085ce31eeec86b39089f905a9092cfbf3f4a12&profile%5Bexperiences%5D%5B0%5D%5Btitle%5D=Stage&profile%5Bexperiences%5D%5B0%5D%5Bdescription%5D=Stage%3A+D%C3%A9veloppement+d%27une+application+web+pour+la+gestion+et+l%27archivage+des+soutenances+de%0Ath%C3%A8ses.%0A%E2%80%A2%0A%E2%80%A2%0ACoordinated+project+tasks%2C+ensuring+adherence+to+project+requirements+and+deadlines.%0AConducted+comprehensive+project+analyses%2C+identifying+and+rectifying+discrepancies+in+work%0Aflow+designs.&profile%5Bexperiences%5D%5B0%5D%5Bdate_start%5D=2024-01-01T00%3A00%3A00&profile%5Bexperiences%5D%5B0%5D%5Bdate_end%5D=2024-12-31T00%3A00%3A00&profile%5Bexperiences%5D%5B0%5D%5Btasks%5D%5B0%5D%5Bname%5D=conducted+comprehensive+project+analyses&profile%5Bexperiences%5D%5B0%5D%5Btasks%5D%5B1%5D%5Bname%5D=coordinated+project+tasks&profile%5Bexperiences%5D%5B0%5D%5Btasks%5D%5B2%5D%5Bname%5D=d%C3%A9veloppement+d%27une+application+web+pour+la+gestion+et+l%27archivage+des+soutenances+de+th%C3%A8ses&profile%5Bexperiences%5D%5B0%5D%5Btasks%5D%5B3%5D%5Bname%5D=ensuring+adherence+to+project+requirements+and+deadlines&profile%5Bexperiences%5D%5B0%5D%5Btasks%5D%5B4%5D%5Bname%5D=identifying+and+rectifying+discrepancies+in+work+flow+designs&profile%5Bexperiences%5D%5B0%5D%5Bcompany%5D=&profile%5Beducations%5D%5B0%5D%5Bkey%5D=05549db885dfdb2b967bae726735b86435f621e6&profile%5Beducations%5D%5B0%5D%5Btitle%5D=Master+of+Cyber+security+and+big+data&profile%5Beducations%5D%5B0%5D%5Bdescription%5D=Master+of+Cyber+security+and+big+data%0AFST+Abdelmalek+Essa%C3%A2di+University+Tangier+Morocco%0A%E2%80%A2%0A%E2%80%A2%0ASpecialization+in+systems+security.%0ABig+data+management+and+analytics.%0AMachine+Learning+and+Al+Algorithms.%0A-+ongoing&profile%5Beducations%5D%5B0%5D%5Bdate_start%5D=2024-09-01T00%3A00%3A00&profile%5Beducations%5D%5B0%5D%5Bdate_end%5D=2024-09-30T00%3A00%3A00&profile%5Beducations%5D%5B0%5D%5Bskills%5D%5B0%5D%5Bname%5D=al+algorithms&profile%5Beducations%5D%5B0%5D%5Bskills%5D%5B0%5D%5Btype%5D=hard&profile%5Beducations%5D%5B0%5D%5Bskills%5D%5B1%5D%5Bname%5D=machine+learning&profile%5Beducations%5D%5B0%5D%5Bskills%5D%5B1%5D%5Btype%5D=hard&profile%5Beducations%5D%5B0%5D%5Btasks%5D%5B0%5D%5Bname%5D=big+data+management+and+analytics&profile%5Beducations%5D%5B0%5D%5Bschool%5D=FST+Abdelmalek+Essa%C3%A2di+University+Tangier+Morocco&profile%5Beducations%5D%5B1%5D%5Bkey%5D=8f720bd717b68cc159bf65dbac8bd9638b5f3ed5&profile%5Beducations%5D%5B1%5D%5Btitle%5D=Bachelor+of+computer+engineering&profile%5Beducations%5D%5B1%5D%5Bdescription%5D=Bachelor+of+computer+engineering%0AFST-+Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco%0AProgramming+and+Software+Development.%0A%E2%80%A2%0ANetworking+and+Distributed+Systems.%0A.%0AComputer+Systems+and+Architecture.&profile%5Beducations%5D%5B1%5D%5Bdate_start%5D=2024-01-01T00%3A00%3A00&profile%5Beducations%5D%5B1%5D%5Bdate_end%5D=2024-12-31T00%3A00%3A00&profile%5Beducations%5D%5B1%5D%5Bschool%5D=FST-+Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco&profile%5Beducations%5D%5B2%5D%5Bkey%5D=8e92fd7a932b5eaac9b68f2a2e863f5aba3dd112&profile%5Beducations%5D%5B2%5D%5Btitle%5D=Diploma+in+Science+and+Technology+%28MIPC%29&profile%5Beducations%5D%5B2%5D%5Bdescription%5D=Diploma+in+Science+and+Technology+%28MIPC%29%0AFST-Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco&profile%5Beducations%5D%5B2%5D%5Bdate_start%5D=2023-01-01T00%3A00%3A00&profile%5Beducations%5D%5B2%5D%5Bdate_end%5D=2023-12-31T00%3A00%3A00&profile%5Beducations%5D%5B2%5D%5Bschool%5D=FST-Sultan+Moulay+Slimane+University+Beni+Mellal+Morocco&profile%5Beducations%5D%5B3%5D%5Bkey%5D=f4b8038a3af6b799161bb0c64a28519dedd4cc2a&profile%5Beducations%5D%5B3%5D%5Btitle%5D=Baccalaur%C3%A9at+in+Physics+and+chemistry&profile%5Beducations%5D%5B3%5D%5Bdescription%5D=Baccalaur%C3%A9at+in+Physics+and+chemistry%0ALyc%C3%A9e+El+Kindi+Fquih+Ben+Salah+Morocco&profile%5Beducations%5D%5B3%5D%5Bdate_start%5D=2018-01-01T00%3A00%3A00&profile%5Beducations%5D%5B3%5D%5Bdate_end%5D=2018-12-31T00%3A00%3A00&profile%5Beducations%5D%5B3%5D%5Bschool%5D=Lyc%C3%A9e+El+Kindi+Fquih+Ben+Salah+Morocco&profile%5Beducations%5D%5B4%5D%5Bkey%5D=0402e0a96e95feec2a85e2c0fefd25f9db568770&profile%5Beducations%5D%5B4%5D%5Btitle%5D=Big+Data+101&profile%5Beducations%5D%5B4%5D%5Bdescription%5D=.%0ABig+Data+101%3A+IBM+SkillsBuild&profile%5Beducations%5D%5B4%5D%5Bdate_start%5D=2025-06-01T00%3A00%3A00&profile%5Beducations%5D%5B4%5D%5Bdate_end%5D=2025-06-30T00%3A00%3A00&profile%5Beducations%5D%5B4%5D%5Bschool%5D=&profile%5Beducations%5D%5B5%5D%5Bkey%5D=8bf58aa9f3621cf787a4d664c2a5fe07afd226b7&profile%5Beducations%5D%5B5%5D%5Btitle%5D=IBM+SkillsBuild&profile%5Beducations%5D%5B5%5D%5Bdescription%5D=%E2%8B%85%0AHadoop+101%3A+IBM+SkillsBuild&profile%5Beducations%5D%5B5%5D%5Bdate_start%5D=2025-06-01T00%3A00%3A00&profile%5Beducations%5D%5B5%5D%5Bdate_end%5D=2025-06-30T00%3A00%3A00&profile%5Beducations%5D%5B5%5D%5Bskills%5D%5B0%5D%5Bname%5D=hadoop+101&profile%5Beducations%5D%5B5%5D%5Bskills%5D%5B0%5D%5Btype%5D=hard&profile%5Beducations%5D%5B5%5D%5Bschool%5D=&profile%5Battachments%5D%5B0%5D%5Btype%5D=original&profile%5Battachments%5D%5B0%5D%5Balt%5D=640fbc6973240ebd44feaf8ee6e2bb97e99e4fd1&profile%5Battachments%5D%5B0%5D%5Bfile_size%5D=329209&profile%5Battachments%5D%5B0%5D%5Bfile_name%5D=original&profile%5Battachments%5D%5B0%5D%5Boriginal_file_name%5D=file.pdf&profile%5Battachments%5D%5B0%5D%5Bextension%5D=.pdf&profile%5Battachments%5D%5B0%5D%5Bpublic_url%5D=https%3A%2F%2Friminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com%2Fteams%2F67b512d20fed124cc5f67db9beb49dd0b91fd2e7%2Fsources%2F0892ef1f91ea52860edb1fdf8cefd5234a42aa72%2Fprofiles%2F413c041ce188942dac5d749e4ed7df70c44e5519%2Fparsing%2Foriginal.pdf&profile%5Battachments%5D%5B0%5D%5Bupdated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Battachments%5D%5B0%5D%5Bcreated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Battachments%5D%5B1%5D%5Btype%5D=resume&profile%5Battachments%5D%5B1%5D%5Balt%5D=e1d96118f0a9909fee7f00f444149e4fdb8752e6&profile%5Battachments%5D%5B1%5D%5Bfile_size%5D=350215&profile%5Battachments%5D%5B1%5D%5Bfile_name%5D=resume&profile%5Battachments%5D%5B1%5D%5Boriginal_file_name%5D=file.pdf&profile%5Battachments%5D%5B1%5D%5Bextension%5D=.pdf&profile%5Battachments%5D%5B1%5D%5Bpublic_url%5D=https%3A%2F%2Friminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com%2Fteams%2F67b512d20fed124cc5f67db9beb49dd0b91fd2e7%2Fsources%2F0892ef1f91ea52860edb1fdf8cefd5234a42aa72%2Fprofiles%2F413c041ce188942dac5d749e4ed7df70c44e5519%2Fparsing%2Fresume.pdf&profile%5Battachments%5D%5B1%5D%5Bupdated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Battachments%5D%5B1%5D%5Bcreated_at%5D=2025-08-06T19%3A35%3A35%2B0000&profile%5Bskills%5D%5B0%5D%5Bname%5D=agile&profile%5Bskills%5D%5B0%5D%5Btype%5D=hard&profile%5Bskills%5D%5B1%5D%5Bname%5D=ai&profile%5Bskills%5D%5B1%5D%5Btype%5D=hard&profile%5Bskills%5D%5B2%5D%5Bname%5D=al+algorithms&profile%5Bskills%5D%5B2%5D%5Btype%5D=hard&profile%5Bskills%5D%5B3%5D%5Bname%5D=angular+frontend&profile%5Bskills%5D%5B3%5D%5Btype%5D=hard&profile%5Bskills%5D%5B4%5D%5Bname%5D=bootstrap&profile%5Bskills%5D%5B4%5D%5Btype%5D=hard&profile%5Bskills%5D%5B5%5D%5Bname%5D=cloud+computing&profile%5Bskills%5D%5B5%5D%5Btype%5D=hard&profile%5Bskills%5D%5B6%5D%5Bname%5D=cloudsim&profile%5Bskills%5D%5B6%5D%5Btype%5D=hard&profile%5Bskills%5D%5B7%5D%5Bname%5D=css&profile%5Bskills%5D%5B7%5D%5Btype%5D=hard&profile%5Bskills%5D%5B8%5D%5Bname%5D=ethereum+blockchain&profile%5Bskills%5D%5B8%5D%5Btype%5D=hard&profile%5Bskills%5D%5B9%5D%5Bname%5D=ethereum+transactions&profile%5Bskills%5D%5B9%5D%5Btype%5D=hard&profile%5Bskills%5D%5B10%5D%5Bname%5D=fastapi&profile%5Bskills%5D%5B10%5D%5Btype%5D=hard&profile%5Bskills%5D%5B11%5D%5Bname%5D=fintech&profile%5Bskills%5D%5B11%5D%5Btype%5D=hard&profile%5Bskills%5D%5B12%5D%5Bname%5D=fintech+application&profile%5Bskills%5D%5B12%5D%5Btype%5D=hard&profile%5Bskills%5D%5B13%5D%5Bname%5D=hadoop+101&profile%5Bskills%5D%5B13%5D%5Btype%5D=hard&profile%5Bskills%5D%5B14%5D%5Bname%5D=html5&profile%5Bskills%5D%5B14%5D%5Btype%5D=hard&profile%5Bskills%5D%5B15%5D%5Bname%5D=java&profile%5Bskills%5D%5B15%5D%5Btype%5D=hard&profile%5Bskills%5D%5B16%5D%5Bname%5D=kubeflow&profile%5Bskills%5D%5B16%5D%5Btype%5D=hard&profile%5Bskills%5D%5B17%5D%5Bname%5D=laas&profile%5Bskills%5D%5B17%5D%5Btype%5D=hard&profile%5Bskills%5D%5B18%5D%5Bname%5D=laravel&profile%5Bskills%5D%5B18%5D%5Btype%5D=hard&profile%5Bskills%5D%5B19%5D%5Bname%5D=linux&profile%5Bskills%5D%5B19%5D%5Btype%5D=hard&profile%5Bskills%5D%5B20%5D%5Bname%5D=machine+learning&profile%5Bskills%5D%5B20%5D%5Btype%5D=hard&profile%5Bskills%5D%5B21%5D%5Bname%5D=machine+learning+data+mining&profile%5Bskills%5D%5B21%5D%5Btype%5D=hard&profile%5Bskills%5D%5B22%5D%5Bname%5D=malware+analysis&profile%5Bskills%5D%5B22%5D%5Btype%5D=hard&profile%5Bskills%5D%5B23%5D%5Bname%5D=merise&profile%5Bskills%5D%5B23%5D%5Btype%5D=hard&profile%5Bskills%5D%5B24%5D%5Bname%5D=metamask&profile%5Bskills%5D%5B24%5D%5Btype%5D=hard&profile%5Bskills%5D%5B25%5D%5Bname%5D=mlops&profile%5Bskills%5D%5B25%5D%5Btype%5D=hard&profile%5Bskills%5D%5B26%5D%5Bname%5D=nosql&profile%5Bskills%5D%5B26%5D%5Btype%5D=hard&profile%5Bskills%5D%5B27%5D%5Bname%5D=openstack&profile%5Bskills%5D%5B27%5D%5Btype%5D=hard&profile%5Bskills%5D%5B28%5D%5Bname%5D=oracle&profile%5Bskills%5D%5B28%5D%5Btype%5D=hard&profile%5Bskills%5D%5B29%5D%5Bname%5D=php&profile%5Bskills%5D%5B29%5D%5Btype%5D=hard&profile%5Bskills%5D%5B30%5D%5Bname%5D=python&profile%5Bskills%5D%5B30%5D%5Btype%5D=hard&profile%5Bskills%5D%5B31%5D%5Bname%5D=saas&profile%5Bskills%5D%5B31%5D%5Btype%5D=hard&profile%5Bskills%5D%5B32%5D%5Bname%5D=scrum&profile%5Bskills%5D%5B32%5D%5Btype%5D=hard&profile%5Bskills%5D%5B33%5D%5Bname%5D=sla&profile%5Bskills%5D%5B33%5D%5Btype%5D=hard&profile%5Bskills%5D%5B34%5D%5Bname%5D=solidity+smart&profile%5Bskills%5D%5B34%5D%5Btype%5D=hard&profile%5Bskills%5D%5B35%5D%5Bname%5D=sql&profile%5Bskills%5D%5B35%5D%5Btype%5D=hard&profile%5Bskills%5D%5B36%5D%5Bname%5D=sql+server&profile%5Bskills%5D%5B36%5D%5Btype%5D=hard&profile%5Bskills%5D%5B37%5D%5Bname%5D=uml&profile%5Bskills%5D%5B37%5D%5Btype%5D=hard&profile%5Bskills%5D%5B38%5D%5Bname%5D=web3&profile%5Bskills%5D%5B38%5D%5Btype%5D=hard&profile%5Blanguages%5D%5B0%5D%5Bname%5D=arabic&profile%5Blanguages%5D%5B1%5D%5Bname%5D=english&profile%5Blanguages%5D%5B2%5D%5Bname%5D=french&profile%5Bcertifications%5D%5B0%5D%5Bname%5D=ibm+skills+build&profile%5Bcertifications%5D%5B1%5D%5Bname%5D=spark+fundamentals+i&profile%5Btasks%5D%5B0%5D%5Bname%5D=adaptation%3A+fast+at+adapting+to+new+scenarios%2C+and+work+flows&profile%5Btasks%5D%5B1%5D%5Bname%5D=big+data+management+and+analytics&profile%5Btasks%5D%5B2%5D%5Bname%5D=conducted+comprehensive+project+analyses&profile%5Btasks%5D%5B3%5D%5Bname%5D=coordinated+project+tasks&profile%5Btasks%5D%5B4%5D%5Bname%5D=creating+a+multiple+agent+system+using+jade&profile%5Btasks%5D%5B5%5D%5Bname%5D=designing+and+implementing+an+unsupervised+learning+pipeline+for+malware+analysis&profile%5Btasks%5D%5B6%5D%5Bname%5D=designing+and+simulating+a+cloud+computing+environment&profile%5Btasks%5D%5B7%5D%5Bname%5D=detailed+analysis+with+comprehensive+reporting&profile%5Btasks%5D%5B8%5D%5Bname%5D=developed+a+simulation+to+optimize+resource+allocation&profile%5Btasks%5D%5B9%5D%5Bname%5D=d%C3%A9veloppement+d%27une+application+web+pour+la+gestion+et+l%27archivage+des+soutenances+de+th%C3%A8ses&profile%5Btasks%5D%5B10%5D%5Bname%5D=ensuring+adherence+to+project+requirements+and+deadlines&profile%5Btasks%5D%5B11%5D%5Bname%5D=establishing+logic+for+each+agent+and+reviewing+performance&profile%5Btasks%5D%5B12%5D%5Bname%5D=evaluating+scenarios+based+onspecific+requirements&profile%5Btasks%5D%5B13%5D%5Bname%5D=focusing+on+clustering+and+dimensionality+reduction&profile%5Btasks%5D%5B14%5D%5Bname%5D=identifying+and+rectifying+discrepancies+in+work+flow+designs&profile%5Btasks%5D%5B15%5D%5Bname%5D=while+trying+to+optimize+the+behavior&profile%5Binterests%5D%5B0%5D%5Bname%5D=books&profile%5Binterests%5D%5B1%5D%5Bname%5D=light+novels&profile%5Binterests%5D%5B2%5D%5Bname%5D=playing+video+games&profile%5Binterests%5D%5B3%5D%5Bname%5D=reading&profile%5Binterests%5D%5B4%5D%5Bname%5D=travel&profile%5Btags%5D%5B0%5D%5Bname%5D=referrer'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["test_txt"]}, {"cell_type": "code", "execution_count": 7, "id": "70065aca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "{\"type\":\"profile.parsing.success\",\"origin\":\"api\",\"message\":\"Profile parsing succeed\",\"profile\":{\"source_key\":\"0892ef1f91ea52860edb1fdf8cefd5234a42aa72\",\"id\":\"52852532\",\"key\":\"f079642c09c935d9c17f0aa0b14a492204911527\",\"consent_algorithmic\":{\"owner\":{\"parsing\":\"0\",\"revealing\":\"0\",\"embedding\":\"0\",\"searching\":\"0\",\"scoring\":\"0\",\"reasoning\":\"0\"},\"controller\":{\"parsing\":\"0\",\"revealing\":\"0\",\"embedding\":\"0\",\"searching\":\"0\",\"scoring\":\"0\",\"reasoning\":\"0\"}},\"source\":{\"key\":\"0892ef1f91ea52860edb1fdf8cefd5234a42aa72\"},\"updated_at\":\"2025-08-06T21:37:03+0000\",\"created_at\":\"2025-08-06T21:37:03+0000\",\"info\":{\"full_name\":\"AYMAN FRIMANE\",\"first_name\":\"AYMAN\",\"last_name\":\"FRIMANE\",\"email\":\"<EMAIL>\",\"phone\":\"0623608844\",\"urls\":{\"0\":{\"type\":\"linkedin\",\"url\":\"https://www.linkedin.com/in/ayman-frimane-1601922b5\"}},\"picture\":\"https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/picture.png\",\"gender\":\"male\",\"summary\":\"A Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.\"},\"text_language\":\"en\",\"text\":\"AYMAN FRIMANE\\<EMAIL>\\n\\n0623608844\\n\\nFquih Ben Salah\\n\\nin linkedin.com/in/ayman-frimane-1601922b5\\n\\nSUMMARY\\n\\nA Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.\\n\\nEXPERIENCE\\n\\n2025\\n\\nProject: FinTech Application for Ethereum Transactions and Exchanges\\n\\n•\\n\\nDeveloped an innovative FinTech platform leveraging Ethereum blockchain and\\nmicroservices to manage secure, decentralized e-transactions and cryptocurrency\\nexchanges using Solidity smart contracts. Integrated Web3 and MetaMask for intuitive user\\ninteraction, ensuring scalability, security, and performance while addressing technical\\nchallenges in platform development.\\nProject: Unsupervised Learning for Malware Analysis\\n\\n.\\n\\nDesigning and implementing an unsupervised learning pipeline for malware analysis,\\nfocusing on clustering and dimensionality reduction, with deployment via FastAPI, an\\nAngular frontend, MLOps practices using Kubeflow, and detailed analysis with\\ncomprehensive reporting.\\n\\nProject: Designing and simulating a cloud computing environment\\n\\n.\\n\\nDesigned and simulated a Cloud Computing architecture using CloudSim, evaluating\\nscenarios based onspecific requirements. Implemented an OpenStack-based laaS with\\nLinux, developed a SaaS application, and created a Python script to monitor instance\\navailability (99.5% daily uptime) for an SLA, utilizing OpenStack.\\n\\nProject: Creating a multiple agent system using jade\\n\\n•\\n\\nDeveloped a simulation to optimize resource allocation, establishing logic for each agent\\nand reviewing performance, while trying to optimize the behavior.\\n\\n2024\\n\\nStage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n\\n•\\n\\n•\\n\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\\n\\nEDUCATION\\n\\nMaster of Cyber security and big data\\nFST Abdelmalek Essaâdi University Tangier Morocco\\n\\n•\\n\\n•\\n\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n\\nBachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n\\nSep 2024 - ongoing\\n\\n2024\\n\\n•\\n\\nNetworking and Distributed Systems.\\n\\n.\\n\\nComputer Systems and Architecture.\\n\\nDiploma in Science and Technology (MIPC)\\n\\n2023\\n\\nFST-Sultan Moulay Slimane University Beni Mellal Morocco\\n\\n2018\\n\\nBaccalauréat in Physics and chemistry\\nLycée El Kindi Fquih Ben Salah Morocco\\n\\nCOMPETENCIES\\n\\n.\\n\\n•\\n\\nProgramming: Java, Python, Php\\nWeb Technologies: HTML5, CSS, Laravel,\\nBootstrap.\\n• Data base management: SQL, NoSQL, Oracle,\\nSQL server.\\n\\n•\\n\\n• Modularisation: UML, Merise.\\nProject management: Agile, Scrum\\nAI, Machine learning Data mining.\\n\\n• Team work: able to work well with diverse\\ntypes of people.\\n\\n•\\n\\nAdaptation: fast at adapting to new\\nscenarios, and work flows\\nProblem Solving: fast at thinking and trying\\nnew solutions to rising problems.\\nLanguages: Arabic, English, French\\n\\nCERTIFICATES\\n\\n.\\n\\nBig Data 101: IBM SkillsBuild june 2025\\n\\n⋅\\n\\nHadoop 101: IBM SkillsBuild june 2025\\n\\n•\\n\\nSpark Fundamentals I: IBM Skills Build june 2025\\n\\nHOBBIES AND INTERESTS\\n\\n•\\n\\nReading: Books and light novels.\\n\\n• Travel: visiting and seeing new places and having new experiences.\\n\\n•\\n\\n•\\n\\nTechnologies : trying to keep us with new inventions, especially in computer tech.\\nGaming: playing video games, helps me unwind and also i find purpose in the challenge of\\neach unique game.\",\"experiences_duration\":\"1\",\"educations_duration\":\"3.0739726027397\",\"experiences\":{\"0\":{\"key\":\"9fa560183c3a01557d0a282368b41668f403801f\",\"title\":\"Stage\",\"description\":\"Stage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n•\\n•\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\",\"date_start\":\"2024-01-01T00:00:00\",\"date_end\":\"2024-12-31T00:00:00\",\"tasks\":{\"0\":{\"name\":\"conducted comprehensive project analyses\"},\"1\":{\"name\":\"coordinated project tasks\"},\"2\":{\"name\":\"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"},\"3\":{\"name\":\"ensuring adherence to project requirements and deadlines\"},\"4\":{\"name\":\"identifying and rectifying discrepancies in work flow designs\"}},\"company\":\"\"}},\"educations\":{\"0\":{\"key\":\"ef3e74a0a347c5d2a75ba0f6b81f264250c31210\",\"title\":\"Master of Cyber security and big data\",\"description\":\"Master of Cyber security and big data\\nFST Abdelmalek Essaâdi University Tangier Morocco\\n•\\n•\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n- ongoing\",\"date_start\":\"2024-09-01T00:00:00\",\"date_end\":\"2024-09-30T00:00:00\",\"skills\":{\"0\":{\"name\":\"al algorithms\",\"type\":\"hard\"},\"1\":{\"name\":\"machine learning\",\"type\":\"hard\"}},\"tasks\":{\"0\":{\"name\":\"big data management and analytics\"}},\"school\":\"FST Abdelmalek Essaâdi University Tangier Morocco\"},\"1\":{\"key\":\"2098a3244284c3142f8cfacd786108cad8cec90c\",\"title\":\"Bachelor of computer engineering\",\"description\":\"Bachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n•\\nNetworking and Distributed Systems.\\n.\\nComputer Systems and Architecture.\",\"date_start\":\"2024-01-01T00:00:00\",\"date_end\":\"2024-12-31T00:00:00\",\"school\":\"FST- Sultan Moulay Slimane University Beni Mellal Morocco\"},\"2\":{\"key\":\"765d100cc839f97288a5c8074db9b2e46587e6e9\",\"title\":\"Diploma in Science and Technology (MIPC)\",\"description\":\"Diploma in Science and Technology (MIPC)\\nFST-Sultan Moulay Slimane University Beni Mellal Morocco\",\"date_start\":\"2023-01-01T00:00:00\",\"date_end\":\"2023-12-31T00:00:00\",\"school\":\"FST-Sultan Moulay Slimane University Beni Mellal Morocco\"},\"3\":{\"key\":\"4503a65c610c170189af9d17e3e6976abdc3047b\",\"title\":\"Baccalauréat in Physics and chemistry\",\"description\":\"Baccalauréat in Physics and chemistry\\nLycée El Kindi Fquih Ben Salah Morocco\",\"date_start\":\"2018-01-01T00:00:00\",\"date_end\":\"2018-12-31T00:00:00\",\"school\":\"Lycée El Kindi Fquih Ben Salah Morocco\"},\"4\":{\"key\":\"a72e6b588bca5482198979163db04bb23214315b\",\"title\":\"Big Data 101\",\"description\":\".\\nBig Data 101: IBM SkillsBuild\",\"date_start\":\"2025-06-01T00:00:00\",\"date_end\":\"2025-06-30T00:00:00\",\"school\":\"\"},\"5\":{\"key\":\"7fa8e9b7c09bf7bc6acaccc1b5a1bb3c2db424af\",\"title\":\"IBM SkillsBuild\",\"description\":\"⋅\\nHadoop 101: IBM SkillsBuild\",\"date_start\":\"2025-06-01T00:00:00\",\"date_end\":\"2025-06-30T00:00:00\",\"skills\":{\"0\":{\"name\":\"hadoop 101\",\"type\":\"hard\"}},\"school\":\"\"}},\"attachments\":{\"0\":{\"type\":\"original\",\"alt\":\"640fbc6973240ebd44feaf8ee6e2bb97e99e4fd1\",\"file_size\":\"329209\",\"file_name\":\"original\",\"original_file_name\":\"file.pdf\",\"extension\":\".pdf\",\"public_url\":\"https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/original.pdf\",\"updated_at\":\"2025-08-06T21:37:03+0000\",\"created_at\":\"2025-08-06T21:37:03+0000\"},\"1\":{\"type\":\"resume\",\"alt\":\"c9adbb868cb2dabcea75fed2144631e0c2d3ac2a\",\"file_size\":\"350379\",\"file_name\":\"resume\",\"original_file_name\":\"file.pdf\",\"extension\":\".pdf\",\"public_url\":\"https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/resume.pdf\",\"updated_at\":\"2025-08-06T21:37:03+0000\",\"created_at\":\"2025-08-06T21:37:03+0000\"}},\"skills\":{\"0\":{\"name\":\"agile\",\"type\":\"hard\"},\"1\":{\"name\":\"ai\",\"type\":\"hard\"},\"2\":{\"name\":\"al algorithms\",\"type\":\"hard\"},\"3\":{\"name\":\"angular frontend\",\"type\":\"hard\"},\"4\":{\"name\":\"bootstrap\",\"type\":\"hard\"},\"5\":{\"name\":\"cloud computing\",\"type\":\"hard\"},\"6\":{\"name\":\"cloudsim\",\"type\":\"hard\"},\"7\":{\"name\":\"css\",\"type\":\"hard\"},\"8\":{\"name\":\"ethereum blockchain\",\"type\":\"hard\"},\"9\":{\"name\":\"ethereum transactions\",\"type\":\"hard\"},\"10\":{\"name\":\"fastapi\",\"type\":\"hard\"},\"11\":{\"name\":\"fintech\",\"type\":\"hard\"},\"12\":{\"name\":\"fintech application\",\"type\":\"hard\"},\"13\":{\"name\":\"hadoop 101\",\"type\":\"hard\"},\"14\":{\"name\":\"html5\",\"type\":\"hard\"},\"15\":{\"name\":\"java\",\"type\":\"hard\"},\"16\":{\"name\":\"kubeflow\",\"type\":\"hard\"},\"17\":{\"name\":\"laas\",\"type\":\"hard\"},\"18\":{\"name\":\"laravel\",\"type\":\"hard\"},\"19\":{\"name\":\"linux\",\"type\":\"hard\"},\"20\":{\"name\":\"machine learning\",\"type\":\"hard\"},\"21\":{\"name\":\"machine learning data mining\",\"type\":\"hard\"},\"22\":{\"name\":\"malware analysis\",\"type\":\"hard\"},\"23\":{\"name\":\"merise\",\"type\":\"hard\"},\"24\":{\"name\":\"metamask\",\"type\":\"hard\"},\"25\":{\"name\":\"mlops\",\"type\":\"hard\"},\"26\":{\"name\":\"nosql\",\"type\":\"hard\"},\"27\":{\"name\":\"openstack\",\"type\":\"hard\"},\"28\":{\"name\":\"oracle\",\"type\":\"hard\"},\"29\":{\"name\":\"php\",\"type\":\"hard\"},\"30\":{\"name\":\"python\",\"type\":\"hard\"},\"31\":{\"name\":\"saas\",\"type\":\"hard\"},\"32\":{\"name\":\"scrum\",\"type\":\"hard\"},\"33\":{\"name\":\"sla\",\"type\":\"hard\"},\"34\":{\"name\":\"solidity smart\",\"type\":\"hard\"},\"35\":{\"name\":\"sql\",\"type\":\"hard\"},\"36\":{\"name\":\"sql server\",\"type\":\"hard\"},\"37\":{\"name\":\"uml\",\"type\":\"hard\"},\"38\":{\"name\":\"web3\",\"type\":\"hard\"}},\"languages\":{\"0\":{\"name\":\"arabic\"},\"1\":{\"name\":\"english\"},\"2\":{\"name\":\"french\"}},\"certifications\":{\"0\":{\"name\":\"ibm skills build\"},\"1\":{\"name\":\"spark fundamentals i\"}},\"tasks\":{\"0\":{\"name\":\"adaptation: fast at adapting to new scenarios, and work flows\"},\"1\":{\"name\":\"big data management and analytics\"},\"2\":{\"name\":\"conducted comprehensive project analyses\"},\"3\":{\"name\":\"coordinated project tasks\"},\"4\":{\"name\":\"creating a multiple agent system using jade\"},\"5\":{\"name\":\"designing and implementing an unsupervised learning pipeline for malware analysis\"},\"6\":{\"name\":\"designing and simulating a cloud computing environment\"},\"7\":{\"name\":\"detailed analysis with comprehensive reporting\"},\"8\":{\"name\":\"developed a simulation to optimize resource allocation\"},\"9\":{\"name\":\"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"},\"10\":{\"name\":\"ensuring adherence to project requirements and deadlines\"},\"11\":{\"name\":\"establishing logic for each agent and reviewing performance\"},\"12\":{\"name\":\"evaluating scenarios based onspecific requirements\"},\"13\":{\"name\":\"focusing on clustering and dimensionality reduction\"},\"14\":{\"name\":\"identifying and rectifying discrepancies in work flow designs\"},\"15\":{\"name\":\"while trying to optimize the behavior\"}},\"interests\":{\"0\":{\"name\":\"books\"},\"1\":{\"name\":\"light novels\"},\"2\":{\"name\":\"playing video games\"},\"3\":{\"name\":\"reading\"},\"4\":{\"name\":\"travel\"}},\"tags\":{\"0\":{\"name\":\"referrer\"}}}}\n"]}], "source": ["import requests\n", "\n", "url = \"http://localhost:8000/api/v1/webhooks/debug\"\n", "headers = {\"Content-Type\": \"application/x-www-form-urlencoded\"}\n", "data = test_txt\n", "\n", "response = requests.post(url, headers=headers, data=data)\n", "print(response.status_code)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 11, "id": "01c3f38d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["print(nested.get(\"profile\"))"]}, {"cell_type": "code", "execution_count": 2, "id": "b7727b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting qs-codec\n", "  Downloading qs_codec-1.1.7-py3-none-any.whl.metadata (38 kB)\n", "Downloading qs_codec-1.1.7-py3-none-any.whl (31 kB)\n", "Installing collected packages: qs-codec\n", "Successfully installed qs-codec-1.1.7\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install qs-codec"]}, {"cell_type": "code", "execution_count": 9, "id": "de0bf388", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'qs'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# pip install qs\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mqs\u001b[39;00m\n\u001b[32m      4\u001b[39m url_encoded_string = test_txt\n\u001b[32m      6\u001b[39m nested_dict = qs.parse(url_encoded_string)\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'qs'"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cbe70f62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "139e4cf8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'type': 'profile.parsing.success', 'origin': 'api', 'message': 'Profile parsing succeed', 'profile': {'source_key': '0892ef1f91ea52860edb1fdf8cefd5234a42aa72', 'id': '52852532', 'key': 'f079642c09c935d9c17f0aa0b14a492204911527', 'consent_algorithmic': {'owner': {'parsing': '0', 'revealing': '0', 'embedding': '0', 'searching': '0', 'scoring': '0', 'reasoning': '0'}, 'controller': {'parsing': '0', 'revealing': '0', 'embedding': '0', 'searching': '0', 'scoring': '0', 'reasoning': '0'}}, 'source': {'key': '0892ef1f91ea52860edb1fdf8cefd5234a42aa72'}, 'updated_at': '2025-08-06T21:37:03+0000', 'created_at': '2025-08-06T21:37:03+0000', 'info': {'full_name': 'AYMAN FRIMANE', 'first_name': 'AYMAN', 'last_name': 'FRIMANE', 'email': '<EMAIL>', 'phone': '0623608844', 'urls': {'0': {'type': 'linkedin', 'url': 'https://www.linkedin.com/in/ayman-frimane-1601922b5'}}, 'picture': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/picture.png', 'gender': 'male', 'summary': 'A Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.'}, 'text_language': 'en', 'text': \"AYMAN FRIMANE\\<EMAIL>\\n\\n0623608844\\n\\nFquih Ben Salah\\n\\nin linkedin.com/in/ayman-frimane-1601922b5\\n\\nSUMMARY\\n\\nA Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.\\n\\nEXPERIENCE\\n\\n2025\\n\\nProject: FinTech Application for Ethereum Transactions and Exchanges\\n\\n•\\n\\nDeveloped an innovative FinTech platform leveraging Ethereum blockchain and\\nmicroservices to manage secure, decentralized e-transactions and cryptocurrency\\nexchanges using Solidity smart contracts. Integrated Web3 and MetaMask for intuitive user\\ninteraction, ensuring scalability, security, and performance while addressing technical\\nchallenges in platform development.\\nProject: Unsupervised Learning for Malware Analysis\\n\\n.\\n\\nDesigning and implementing an unsupervised learning pipeline for malware analysis,\\nfocusing on clustering and dimensionality reduction, with deployment via FastAPI, an\\nAngular frontend, MLOps practices using Kubeflow, and detailed analysis with\\ncomprehensive reporting.\\n\\nProject: Designing and simulating a cloud computing environment\\n\\n.\\n\\nDesigned and simulated a Cloud Computing architecture using CloudSim, evaluating\\nscenarios based onspecific requirements. Implemented an OpenStack-based laaS with\\nLinux, developed a SaaS application, and created a Python script to monitor instance\\navailability (99.5% daily uptime) for an SLA, utilizing OpenStack.\\n\\nProject: Creating a multiple agent system using jade\\n\\n•\\n\\nDeveloped a simulation to optimize resource allocation, establishing logic for each agent\\nand reviewing performance, while trying to optimize the behavior.\\n\\n2024\\n\\nStage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n\\n•\\n\\n•\\n\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\\n\\nEDUCATION\\n\\nMaster of Cyber security and big data\\nFST Abdelmalek Essaâdi University Tangier Morocco\\n\\n•\\n\\n•\\n\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n\\nBachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n\\nSep 2024 - ongoing\\n\\n2024\\n\\n•\\n\\nNetworking and Distributed Systems.\\n\\n.\\n\\nComputer Systems and Architecture.\\n\\nDiploma in Science and Technology (MIPC)\\n\\n2023\\n\\nFST-Sultan Moulay Slimane University Beni Mellal Morocco\\n\\n2018\\n\\nBaccalauréat in Physics and chemistry\\nLycée El Kindi Fquih Ben Salah Morocco\\n\\nCOMPETENCIES\\n\\n.\\n\\n•\\n\\nProgramming: Java, Python, Php\\nWeb Technologies: HTML5, CSS, Laravel,\\nBootstrap.\\n• Data base management: SQL, NoSQL, Oracle,\\nSQL server.\\n\\n•\\n\\n• Modularisation: UML, Merise.\\nProject management: Agile, Scrum\\nAI, Machine learning Data mining.\\n\\n• Team work: able to work well with diverse\\ntypes of people.\\n\\n•\\n\\nAdaptation: fast at adapting to new\\nscenarios, and work flows\\nProblem Solving: fast at thinking and trying\\nnew solutions to rising problems.\\nLanguages: Arabic, English, French\\n\\nCERTIFICATES\\n\\n.\\n\\nBig Data 101: IBM SkillsBuild june 2025\\n\\n⋅\\n\\nHadoop 101: IBM SkillsBuild june 2025\\n\\n•\\n\\nSpark Fundamentals I: IBM Skills Build june 2025\\n\\nHOBBIES AND INTERESTS\\n\\n•\\n\\nReading: Books and light novels.\\n\\n• Travel: visiting and seeing new places and having new experiences.\\n\\n•\\n\\n•\\n\\nTechnologies : trying to keep us with new inventions, especially in computer tech.\\nGaming: playing video games, helps me unwind and also i find purpose in the challenge of\\neach unique game.\", 'experiences_duration': '1', 'educations_duration': '3.0739726027397', 'experiences': {'0': {'key': '9fa560183c3a01557d0a282368b41668f403801f', 'title': 'Stage', 'description': \"Stage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n•\\n•\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\", 'date_start': '2024-01-01T00:00:00', 'date_end': '2024-12-31T00:00:00', 'tasks': {'0': {'name': 'conducted comprehensive project analyses'}, '1': {'name': 'coordinated project tasks'}, '2': {'name': \"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"}, '3': {'name': 'ensuring adherence to project requirements and deadlines'}, '4': {'name': 'identifying and rectifying discrepancies in work flow designs'}}, 'company': ''}}, 'educations': {'0': {'key': 'ef3e74a0a347c5d2a75ba0f6b81f264250c31210', 'title': 'Master of Cyber security and big data', 'description': 'Master of Cyber security and big data\\nFST Abdelmalek Essaâdi University Tangier Morocco\\n•\\n•\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n- ongoing', 'date_start': '2024-09-01T00:00:00', 'date_end': '2024-09-30T00:00:00', 'skills': {'0': {'name': 'al algorithms', 'type': 'hard'}, '1': {'name': 'machine learning', 'type': 'hard'}}, 'tasks': {'0': {'name': 'big data management and analytics'}}, 'school': 'FST Abdelmalek Essaâdi University Tangier Morocco'}, '1': {'key': '2098a3244284c3142f8cfacd786108cad8cec90c', 'title': 'Bachelor of computer engineering', 'description': 'Bachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n•\\nNetworking and Distributed Systems.\\n.\\nComputer Systems and Architecture.', 'date_start': '2024-01-01T00:00:00', 'date_end': '2024-12-31T00:00:00', 'school': 'FST- Sultan Moulay Slimane University Beni Mellal Morocco'}, '2': {'key': '765d100cc839f97288a5c8074db9b2e46587e6e9', 'title': 'Diploma in Science and Technology (MIPC)', 'description': 'Diploma in Science and Technology (MIPC)\\nFST-Sultan Moulay Slimane University Beni Mellal Morocco', 'date_start': '2023-01-01T00:00:00', 'date_end': '2023-12-31T00:00:00', 'school': 'FST-Sultan Moulay Slimane University Beni Mellal Morocco'}, '3': {'key': '4503a65c610c170189af9d17e3e6976abdc3047b', 'title': 'Baccalauréat in Physics and chemistry', 'description': 'Baccalauréat in Physics and chemistry\\nLycée El Kindi Fquih Ben Salah Morocco', 'date_start': '2018-01-01T00:00:00', 'date_end': '2018-12-31T00:00:00', 'school': 'Lycée El Kindi Fquih Ben Salah Morocco'}, '4': {'key': 'a72e6b588bca5482198979163db04bb23214315b', 'title': 'Big Data 101', 'description': '.\\nBig Data 101: IBM SkillsBuild', 'date_start': '2025-06-01T00:00:00', 'date_end': '2025-06-30T00:00:00', 'school': ''}, '5': {'key': '7fa8e9b7c09bf7bc6acaccc1b5a1bb3c2db424af', 'title': 'IBM SkillsBuild', 'description': '⋅\\nHadoop 101: IBM SkillsBuild', 'date_start': '2025-06-01T00:00:00', 'date_end': '2025-06-30T00:00:00', 'skills': {'0': {'name': 'hadoop 101', 'type': 'hard'}}, 'school': ''}}, 'attachments': {'0': {'type': 'original', 'alt': '640fbc6973240ebd44feaf8ee6e2bb97e99e4fd1', 'file_size': '329209', 'file_name': 'original', 'original_file_name': 'file.pdf', 'extension': '.pdf', 'public_url': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/original.pdf', 'updated_at': '2025-08-06T21:37:03+0000', 'created_at': '2025-08-06T21:37:03+0000'}, '1': {'type': 'resume', 'alt': 'c9adbb868cb2dabcea75fed2144631e0c2d3ac2a', 'file_size': '350379', 'file_name': 'resume', 'original_file_name': 'file.pdf', 'extension': '.pdf', 'public_url': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/resume.pdf', 'updated_at': '2025-08-06T21:37:03+0000', 'created_at': '2025-08-06T21:37:03+0000'}}, 'skills': {'0': {'name': 'agile', 'type': 'hard'}, '1': {'name': 'ai', 'type': 'hard'}, '2': {'name': 'al algorithms', 'type': 'hard'}, '3': {'name': 'angular frontend', 'type': 'hard'}, '4': {'name': 'bootstrap', 'type': 'hard'}, '5': {'name': 'cloud computing', 'type': 'hard'}, '6': {'name': 'cloudsim', 'type': 'hard'}, '7': {'name': 'css', 'type': 'hard'}, '8': {'name': 'ethereum blockchain', 'type': 'hard'}, '9': {'name': 'ethereum transactions', 'type': 'hard'}, '10': {'name': 'fastapi', 'type': 'hard'}, '11': {'name': 'fintech', 'type': 'hard'}, '12': {'name': 'fintech application', 'type': 'hard'}, '13': {'name': 'hadoop 101', 'type': 'hard'}, '14': {'name': 'html5', 'type': 'hard'}, '15': {'name': 'java', 'type': 'hard'}, '16': {'name': 'kubeflow', 'type': 'hard'}, '17': {'name': 'laas', 'type': 'hard'}, '18': {'name': 'laravel', 'type': 'hard'}, '19': {'name': 'linux', 'type': 'hard'}, '20': {'name': 'machine learning', 'type': 'hard'}, '21': {'name': 'machine learning data mining', 'type': 'hard'}, '22': {'name': 'malware analysis', 'type': 'hard'}, '23': {'name': 'merise', 'type': 'hard'}, '24': {'name': 'metamask', 'type': 'hard'}, '25': {'name': 'mlops', 'type': 'hard'}, '26': {'name': 'nosql', 'type': 'hard'}, '27': {'name': 'openstack', 'type': 'hard'}, '28': {'name': 'oracle', 'type': 'hard'}, '29': {'name': 'php', 'type': 'hard'}, '30': {'name': 'python', 'type': 'hard'}, '31': {'name': 'saas', 'type': 'hard'}, '32': {'name': 'scrum', 'type': 'hard'}, '33': {'name': 'sla', 'type': 'hard'}, '34': {'name': 'solidity smart', 'type': 'hard'}, '35': {'name': 'sql', 'type': 'hard'}, '36': {'name': 'sql server', 'type': 'hard'}, '37': {'name': 'uml', 'type': 'hard'}, '38': {'name': 'web3', 'type': 'hard'}}, 'languages': {'0': {'name': 'arabic'}, '1': {'name': 'english'}, '2': {'name': 'french'}}, 'certifications': {'0': {'name': 'ibm skills build'}, '1': {'name': 'spark fundamentals i'}}, 'tasks': {'0': {'name': 'adaptation: fast at adapting to new scenarios, and work flows'}, '1': {'name': 'big data management and analytics'}, '2': {'name': 'conducted comprehensive project analyses'}, '3': {'name': 'coordinated project tasks'}, '4': {'name': 'creating a multiple agent system using jade'}, '5': {'name': 'designing and implementing an unsupervised learning pipeline for malware analysis'}, '6': {'name': 'designing and simulating a cloud computing environment'}, '7': {'name': 'detailed analysis with comprehensive reporting'}, '8': {'name': 'developed a simulation to optimize resource allocation'}, '9': {'name': \"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"}, '10': {'name': 'ensuring adherence to project requirements and deadlines'}, '11': {'name': 'establishing logic for each agent and reviewing performance'}, '12': {'name': 'evaluating scenarios based onspecific requirements'}, '13': {'name': 'focusing on clustering and dimensionality reduction'}, '14': {'name': 'identifying and rectifying discrepancies in work flow designs'}, '15': {'name': 'while trying to optimize the behavior'}}, 'interests': {'0': {'name': 'books'}, '1': {'name': 'light novels'}, '2': {'name': 'playing video games'}, '3': {'name': 'reading'}, '4': {'name': 'travel'}}, 'tags': {'0': {'name': 'referrer'}}}}\n"]}], "source": ["import qs_codec  as qs\n", "nested_dict = qs.decode(test_txt)\n", "\n", "print(nested_dict)"]}, {"cell_type": "code", "execution_count": 13, "id": "7b6a25c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'source_key': '0892ef1f91ea52860edb1fdf8cefd5234a42aa72',\n", " 'id': '52852532',\n", " 'key': 'f079642c09c935d9c17f0aa0b14a492204911527',\n", " 'consent_algorithmic': {'owner': {'parsing': '0',\n", "   'revealing': '0',\n", "   'embedding': '0',\n", "   'searching': '0',\n", "   'scoring': '0',\n", "   'reasoning': '0'},\n", "  'controller': {'parsing': '0',\n", "   'revealing': '0',\n", "   'embedding': '0',\n", "   'searching': '0',\n", "   'scoring': '0',\n", "   'reasoning': '0'}},\n", " 'source': {'key': '0892ef1f91ea52860edb1fdf8cefd5234a42aa72'},\n", " 'updated_at': '2025-08-06T21:37:03+0000',\n", " 'created_at': '2025-08-06T21:37:03+0000',\n", " 'info': {'full_name': 'AYMAN FRIMANE',\n", "  'first_name': 'AYMAN',\n", "  'last_name': 'FRIMANE',\n", "  'email': '<EMAIL>',\n", "  'phone': '0623608844',\n", "  'urls': {'0': {'type': 'linkedin',\n", "    'url': 'https://www.linkedin.com/in/ayman-frimane-1601922b5'}},\n", "  'picture': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/picture.png',\n", "  'gender': 'male',\n", "  'summary': 'A Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.'},\n", " 'text_language': 'en',\n", " 'text': \"AYMAN FRIMANE\\<EMAIL>\\n\\n0623608844\\n\\nFquih <PERSON>\\n\\nin linkedin.com/in/ayman-frimane-1601922b5\\n\\nSUMMARY\\n\\nA Masters student in Cyber Security and Big Data, at faculty of science and technology Tangier,\\ncurrently looking for valuable work or study experience to further enhance my own competence, to\\nfurther may future career.\\n\\nEXPERIENCE\\n\\n2025\\n\\nProject: FinTech Application for Ethereum Transactions and Exchanges\\n\\n•\\n\\nDeveloped an innovative FinTech platform leveraging Ethereum blockchain and\\nmicroservices to manage secure, decentralized e-transactions and cryptocurrency\\nexchanges using Solidity smart contracts. Integrated Web3 and MetaMask for intuitive user\\ninteraction, ensuring scalability, security, and performance while addressing technical\\nchallenges in platform development.\\nProject: Unsupervised Learning for Malware Analysis\\n\\n.\\n\\nDesigning and implementing an unsupervised learning pipeline for malware analysis,\\nfocusing on clustering and dimensionality reduction, with deployment via FastAPI, an\\nAngular frontend, MLOps practices using Kubeflow, and detailed analysis with\\ncomprehensive reporting.\\n\\nProject: Designing and simulating a cloud computing environment\\n\\n.\\n\\nDesigned and simulated a Cloud Computing architecture using CloudSim, evaluating\\nscenarios based onspecific requirements. Implemented an OpenStack-based laaS with\\nLinux, developed a SaaS application, and created a Python script to monitor instance\\navailability (99.5% daily uptime) for an SLA, utilizing OpenStack.\\n\\nProject: Creating a multiple agent system using jade\\n\\n•\\n\\nDeveloped a simulation to optimize resource allocation, establishing logic for each agent\\nand reviewing performance, while trying to optimize the behavior.\\n\\n2024\\n\\nStage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n\\n•\\n\\n•\\n\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\\n\\nEDUCATION\\n\\nMaster of Cyber security and big data\\nFST Abdelmalek Essaâdi University Tangier Morocco\\n\\n•\\n\\n•\\n\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n\\nBachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n\\nSep 2024 - ongoing\\n\\n2024\\n\\n•\\n\\nNetworking and Distributed Systems.\\n\\n.\\n\\nComputer Systems and Architecture.\\n\\nDiploma in Science and Technology (MIPC)\\n\\n2023\\n\\nFST-Sultan Moulay Slimane University Beni Mellal Morocco\\n\\n2018\\n\\nBaccalauréat in Physics and chemistry\\nLycée El Kindi Fquih Ben Salah Morocco\\n\\nCOMPETENCIES\\n\\n.\\n\\n•\\n\\nProgramming: Java, Python, Php\\nWeb Technologies: HTML5, CSS, Laravel,\\nBootstrap.\\n• Data base management: SQL, NoSQL, Oracle,\\nSQL server.\\n\\n•\\n\\n• Modularisation: UML, Merise.\\nProject management: Agile, Scrum\\nAI, Machine learning Data mining.\\n\\n• Team work: able to work well with diverse\\ntypes of people.\\n\\n•\\n\\nAdaptation: fast at adapting to new\\nscenarios, and work flows\\nProblem Solving: fast at thinking and trying\\nnew solutions to rising problems.\\nLanguages: Arabic, English, French\\n\\nCERTIFICATES\\n\\n.\\n\\nBig Data 101: IBM SkillsBuild june 2025\\n\\n⋅\\n\\nHadoop 101: IBM SkillsBuild june 2025\\n\\n•\\n\\nSpark Fundamentals I: IBM Skills Build june 2025\\n\\nHOBBIES AND INTERESTS\\n\\n•\\n\\nReading: Books and light novels.\\n\\n• Travel: visiting and seeing new places and having new experiences.\\n\\n•\\n\\n•\\n\\nTechnologies : trying to keep us with new inventions, especially in computer tech.\\nGaming: playing video games, helps me unwind and also i find purpose in the challenge of\\neach unique game.\",\n", " 'experiences_duration': '1',\n", " 'educations_duration': '3.0739726027397',\n", " 'experiences': {'0': {'key': '9fa560183c3a01557d0a282368b41668f403801f',\n", "   'title': 'Stage',\n", "   'description': \"Stage: Développement d'une application web pour la gestion et l'archivage des soutenances de\\nthèses.\\n•\\n•\\nCoordinated project tasks, ensuring adherence to project requirements and deadlines.\\nConducted comprehensive project analyses, identifying and rectifying discrepancies in work\\nflow designs.\",\n", "   'date_start': '2024-01-01T00:00:00',\n", "   'date_end': '2024-12-31T00:00:00',\n", "   'tasks': {'0': {'name': 'conducted comprehensive project analyses'},\n", "    '1': {'name': 'coordinated project tasks'},\n", "    '2': {'name': \"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"},\n", "    '3': {'name': 'ensuring adherence to project requirements and deadlines'},\n", "    '4': {'name': 'identifying and rectifying discrepancies in work flow designs'}},\n", "   'company': ''}},\n", " 'educations': {'0': {'key': 'ef3e74a0a347c5d2a75ba0f6b81f264250c31210',\n", "   'title': 'Master of Cyber security and big data',\n", "   'description': 'Master of Cyber security and big data\\nFST Abd<PERSON>malek Essaâdi University Tangier Morocco\\n•\\n•\\nSpecialization in systems security.\\nBig data management and analytics.\\nMachine Learning and Al Algorithms.\\n- ongoing',\n", "   'date_start': '2024-09-01T00:00:00',\n", "   'date_end': '2024-09-30T00:00:00',\n", "   'skills': {'0': {'name': 'al algorithms', 'type': 'hard'},\n", "    '1': {'name': 'machine learning', 'type': 'hard'}},\n", "   'tasks': {'0': {'name': 'big data management and analytics'}},\n", "   'school': 'FST Abdelmalek Essaâdi University Tangier Morocco'},\n", "  '1': {'key': '2098a3244284c3142f8cfacd786108cad8cec90c',\n", "   'title': 'Bachelor of computer engineering',\n", "   'description': 'Bachelor of computer engineering\\nFST- Sultan Moulay Slimane University Beni Mellal Morocco\\nProgramming and Software Development.\\n•\\nNetworking and Distributed Systems.\\n.\\nComputer Systems and Architecture.',\n", "   'date_start': '2024-01-01T00:00:00',\n", "   'date_end': '2024-12-31T00:00:00',\n", "   'school': 'FST- Sultan <PERSON> University Beni Mellal Morocco'},\n", "  '2': {'key': '765d100cc839f97288a5c8074db9b2e46587e6e9',\n", "   'title': 'Diploma in Science and Technology (MIPC)',\n", "   'description': 'Diploma in Science and Technology (MIPC)\\nFST-Sultan <PERSON> University Beni Mellal Morocco',\n", "   'date_start': '2023-01-01T00:00:00',\n", "   'date_end': '2023-12-31T00:00:00',\n", "   'school': 'FST-Sultan <PERSON> University Beni Mellal Morocco'},\n", "  '3': {'key': '4503a65c610c170189af9d17e3e6976abdc3047b',\n", "   'title': '<PERSON><PERSON><PERSON><PERSON><PERSON> in Physics and chemistry',\n", "   'description': '<PERSON><PERSON><PERSON><PERSON><PERSON> in Physics and chemistry\\nLycée El Kindi Fquih <PERSON>',\n", "   'date_start': '2018-01-01T00:00:00',\n", "   'date_end': '2018-12-31T00:00:00',\n", "   'school': 'Lycée El <PERSON>i F<PERSON> Ben <PERSON>'},\n", "  '4': {'key': 'a72e6b588bca5482198979163db04bb23214315b',\n", "   'title': 'Big Data 101',\n", "   'description': '.\\nBig Data 101: IBM SkillsBuild',\n", "   'date_start': '2025-06-01T00:00:00',\n", "   'date_end': '2025-06-30T00:00:00',\n", "   'school': ''},\n", "  '5': {'key': '7fa8e9b7c09bf7bc6acaccc1b5a1bb3c2db424af',\n", "   'title': 'IBM SkillsBuild',\n", "   'description': '⋅\\nHadoop 101: IBM SkillsBuild',\n", "   'date_start': '2025-06-01T00:00:00',\n", "   'date_end': '2025-06-30T00:00:00',\n", "   'skills': {'0': {'name': 'hadoop 101', 'type': 'hard'}},\n", "   'school': ''}},\n", " 'attachments': {'0': {'type': 'original',\n", "   'alt': '640fbc6973240ebd44feaf8ee6e2bb97e99e4fd1',\n", "   'file_size': '329209',\n", "   'file_name': 'original',\n", "   'original_file_name': 'file.pdf',\n", "   'extension': '.pdf',\n", "   'public_url': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/original.pdf',\n", "   'updated_at': '2025-08-06T21:37:03+0000',\n", "   'created_at': '2025-08-06T21:37:03+0000'},\n", "  '1': {'type': 'resume',\n", "   'alt': 'c9adbb868cb2dabcea75fed2144631e0c2d3ac2a',\n", "   'file_size': '350379',\n", "   'file_name': 'resume',\n", "   'original_file_name': 'file.pdf',\n", "   'extension': '.pdf',\n", "   'public_url': 'https://riminder-documents-eu-2019-12.s3-eu-west-1.amazonaws.com/teams/67b512d20fed124cc5f67db9beb49dd0b91fd2e7/sources/0892ef1f91ea52860edb1fdf8cefd5234a42aa72/profiles/f079642c09c935d9c17f0aa0b14a492204911527/parsing/resume.pdf',\n", "   'updated_at': '2025-08-06T21:37:03+0000',\n", "   'created_at': '2025-08-06T21:37:03+0000'}},\n", " 'skills': {'0': {'name': 'agile', 'type': 'hard'},\n", "  '1': {'name': 'ai', 'type': 'hard'},\n", "  '2': {'name': 'al algorithms', 'type': 'hard'},\n", "  '3': {'name': 'angular frontend', 'type': 'hard'},\n", "  '4': {'name': 'bootstrap', 'type': 'hard'},\n", "  '5': {'name': 'cloud computing', 'type': 'hard'},\n", "  '6': {'name': 'cloudsim', 'type': 'hard'},\n", "  '7': {'name': 'css', 'type': 'hard'},\n", "  '8': {'name': 'ethereum blockchain', 'type': 'hard'},\n", "  '9': {'name': 'ethereum transactions', 'type': 'hard'},\n", "  '10': {'name': 'fastapi', 'type': 'hard'},\n", "  '11': {'name': 'fintech', 'type': 'hard'},\n", "  '12': {'name': 'fintech application', 'type': 'hard'},\n", "  '13': {'name': 'hadoop 101', 'type': 'hard'},\n", "  '14': {'name': 'html5', 'type': 'hard'},\n", "  '15': {'name': 'java', 'type': 'hard'},\n", "  '16': {'name': 'kubeflow', 'type': 'hard'},\n", "  '17': {'name': 'laas', 'type': 'hard'},\n", "  '18': {'name': 'laravel', 'type': 'hard'},\n", "  '19': {'name': 'linux', 'type': 'hard'},\n", "  '20': {'name': 'machine learning', 'type': 'hard'},\n", "  '21': {'name': 'machine learning data mining', 'type': 'hard'},\n", "  '22': {'name': 'malware analysis', 'type': 'hard'},\n", "  '23': {'name': 'merise', 'type': 'hard'},\n", "  '24': {'name': 'metamask', 'type': 'hard'},\n", "  '25': {'name': 'mlops', 'type': 'hard'},\n", "  '26': {'name': 'nosql', 'type': 'hard'},\n", "  '27': {'name': 'openstack', 'type': 'hard'},\n", "  '28': {'name': 'oracle', 'type': 'hard'},\n", "  '29': {'name': 'php', 'type': 'hard'},\n", "  '30': {'name': 'python', 'type': 'hard'},\n", "  '31': {'name': 'saas', 'type': 'hard'},\n", "  '32': {'name': 'scrum', 'type': 'hard'},\n", "  '33': {'name': 'sla', 'type': 'hard'},\n", "  '34': {'name': 'solidity smart', 'type': 'hard'},\n", "  '35': {'name': 'sql', 'type': 'hard'},\n", "  '36': {'name': 'sql server', 'type': 'hard'},\n", "  '37': {'name': 'uml', 'type': 'hard'},\n", "  '38': {'name': 'web3', 'type': 'hard'}},\n", " 'languages': {'0': {'name': 'arabic'},\n", "  '1': {'name': 'english'},\n", "  '2': {'name': 'french'}},\n", " 'certifications': {'0': {'name': 'ibm skills build'},\n", "  '1': {'name': 'spark fundamentals i'}},\n", " 'tasks': {'0': {'name': 'adaptation: fast at adapting to new scenarios, and work flows'},\n", "  '1': {'name': 'big data management and analytics'},\n", "  '2': {'name': 'conducted comprehensive project analyses'},\n", "  '3': {'name': 'coordinated project tasks'},\n", "  '4': {'name': 'creating a multiple agent system using jade'},\n", "  '5': {'name': 'designing and implementing an unsupervised learning pipeline for malware analysis'},\n", "  '6': {'name': 'designing and simulating a cloud computing environment'},\n", "  '7': {'name': 'detailed analysis with comprehensive reporting'},\n", "  '8': {'name': 'developed a simulation to optimize resource allocation'},\n", "  '9': {'name': \"développement d'une application web pour la gestion et l'archivage des soutenances de thèses\"},\n", "  '10': {'name': 'ensuring adherence to project requirements and deadlines'},\n", "  '11': {'name': 'establishing logic for each agent and reviewing performance'},\n", "  '12': {'name': 'evaluating scenarios based onspecific requirements'},\n", "  '13': {'name': 'focusing on clustering and dimensionality reduction'},\n", "  '14': {'name': 'identifying and rectifying discrepancies in work flow designs'},\n", "  '15': {'name': 'while trying to optimize the behavior'}},\n", " 'interests': {'0': {'name': 'books'},\n", "  '1': {'name': 'light novels'},\n", "  '2': {'name': 'playing video games'},\n", "  '3': {'name': 'reading'},\n", "  '4': {'name': 'travel'}},\n", " 'tags': {'0': {'name': 'referrer'}}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["nested_dict.get(\"profile\")"]}, {"cell_type": "code", "execution_count": null, "id": "1bd3ce17", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "resume-parse-match", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}