#!/usr/bin/env python3
"""
Initialize the database with tables and sample data
"""
import asyncio
import sys
import os
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from app.core.config import settings
from app.core.database import Base
from app.models.user import User
from app.models.project import Project
from app.models.candidate import Candidate
from app.core.security import get_password_hash

# Setup basic logging for the script
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_tables():
    """Create all database tables"""
    engine = create_engine(settings.DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    logger.info("✅ Database tables created successfully")


def create_sample_user():
    """Create a sample user for testing"""
    from sqlalchemy.orm import sessionmaker
    
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("✅ Sample user already exists")
            return
        
        # Create sample user
        sample_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="Admin User",
            is_active=True,
            is_superuser=True
        )
        
        db.add(sample_user)
        db.commit()
        logger.info("✅ Sample user created: <EMAIL> / admin123")

    except Exception as e:
        logger.error(f"❌ Error creating sample user: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main initialization function"""
    logger.info("🚀 Initializing CV Scoring Database...")

    try:
        create_tables()
        create_sample_user()
        logger.info("✅ Database initialization completed successfully!")
        logger.info("\n📝 Next steps:")
        logger.info("1. Copy .env.example to .env and configure your settings")
        logger.info("2. Run: uvicorn app.main:app --reload")
        logger.info("3. Visit: http://localhost:8000/docs")
        logger.info("4. Login with: <EMAIL> / admin123")

    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
