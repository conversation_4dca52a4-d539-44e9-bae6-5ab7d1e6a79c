import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Clean existing data (in reverse order of dependencies)
  console.log('🧹 Cleaning existing data...');
  await prisma.phaseScore.deleteMany();
  await prisma.interview.deleteMany();
  await prisma.interviewTemplate.deleteMany();
  await prisma.candidate.deleteMany();
  await prisma.phase.deleteMany();
  await prisma.jobOffer.deleteMany();
  await prisma.fileUpload.deleteMany();
  await prisma.project.deleteMany();

  // Create Projects
  console.log('📁 Creating projects...');

  const project1 = await prisma.project.create({
    data: {
      name: 'Senior Full-Stack Developer - TechCorp',
      description: 'We are looking for an experienced full-stack developer to join our growing team and help build scalable web applications.',
      startDate: new Date('2024-01-15'),
      endDate: new Date('2024-03-15'),
    }
  });

  const project2 = await prisma.project.create({
    data: {
      name: 'AI/ML Engineer - DataTech Solutions',
      description: 'Seeking a talented AI/ML engineer to develop cutting-edge machine learning solutions for our enterprise clients.',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-04-01'),
    }
  });

  const project3 = await prisma.project.create({
    data: {
      name: 'DevOps Engineer - CloudFirst Inc',
      description: 'Looking for a DevOps engineer to manage our cloud infrastructure and implement CI/CD pipelines.',
      startDate: new Date('2024-01-20'),
      endDate: new Date('2024-03-20'),
    }
  });

  // Create Job Offers with detailed requirements
  console.log('💼 Creating job offers...');

  const jobOffer1 = await prisma.jobOffer.create({
    data: {
      title: 'Senior Full-Stack Developer',
      description: 'Join our dynamic team to build next-generation web applications using modern technologies.',
      location: 'San Francisco, CA (Remote OK)',
      salary: '$120,000 - $160,000',
      employmentType: 'FULL_TIME',
      department: 'Engineering',
      experienceLevel: 'SENIOR',
      requiredSkills: JSON.stringify({
        required_skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'PostgreSQL'],
        preferred_skills: ['Next.js', 'Docker', 'AWS', 'GraphQL', 'Redis'],
        min_experience_years: 5,
        required_education: "Bachelor's degree in Computer Science or related field",
        technical_requirements: {
          programming_languages: ['JavaScript', 'TypeScript'],
          frameworks: ['React', 'Node.js', 'Express.js'],
          databases: ['PostgreSQL', 'MongoDB'],
          cloud_platforms: ['AWS', 'Azure']
        },
        required_languages: ['English'],
        remote_work: true
      }),
      applicationDeadline: new Date('2024-03-01'),
      benefits: 'Health insurance, 401k matching, flexible PTO, remote work options',
      projectId: project1.id,
      isActive: true
    }
  });

  const jobOffer2 = await prisma.jobOffer.create({
    data: {
      title: 'AI/ML Engineer',
      description: 'Develop and deploy machine learning models to solve complex business problems.',
      location: 'New York, NY',
      salary: '$140,000 - $180,000',
      employmentType: 'FULL_TIME',
      department: 'Data Science',
      experienceLevel: 'SENIOR',
      requiredSkills: JSON.stringify({
        required_skills: ['Python', 'TensorFlow', 'PyTorch', 'Machine Learning', 'Statistics'],
        preferred_skills: ['MLOps', 'Kubernetes', 'Apache Spark', 'Deep Learning', 'NLP'],
        min_experience_years: 4,
        required_education: "Master's degree in Computer Science, Statistics, or related field",
        technical_requirements: {
          programming_languages: ['Python', 'R', 'SQL'],
          frameworks: ['TensorFlow', 'PyTorch', 'Scikit-learn'],
          databases: ['PostgreSQL', 'MongoDB', 'BigQuery'],
          cloud_platforms: ['AWS', 'GCP', 'Azure']
        },
        required_languages: ['English'],
        remote_work: false
      }),
      applicationDeadline: new Date('2024-03-15'),
      benefits: 'Health insurance, stock options, learning budget, gym membership',
      projectId: project2.id,
      isActive: true
    }
  });

  const jobOffer3 = await prisma.jobOffer.create({
    data: {
      title: 'DevOps Engineer',
      description: 'Manage cloud infrastructure and implement robust CI/CD pipelines.',
      location: 'Austin, TX (Hybrid)',
      salary: '$110,000 - $140,000',
      employmentType: 'FULL_TIME',
      department: 'Infrastructure',
      experienceLevel: 'MID',
      requiredSkills: JSON.stringify({
        required_skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD', 'Linux'],
        preferred_skills: ['Terraform', 'Ansible', 'Jenkins', 'Monitoring', 'Security'],
        min_experience_years: 3,
        required_education: "Bachelor's degree in Computer Science or related field",
        technical_requirements: {
          programming_languages: ['Python', 'Bash', 'Go'],
          frameworks: ['Docker', 'Kubernetes'],
          databases: ['PostgreSQL', 'Redis'],
          cloud_platforms: ['AWS', 'Azure']
        },
        required_languages: ['English'],
        remote_work: true
      }),
      applicationDeadline: new Date('2024-03-10'),
      benefits: 'Health insurance, 401k, flexible hours, professional development',
      projectId: project3.id,
      isActive: true
    }
  });

  // Create Phases for each project
  console.log('📋 Creating recruitment phases...');

  // Project 1 Phases (Full-Stack Developer)
  const p1_phase1 = await prisma.phase.create({
    data: {
      name: 'Application Review',
      type: 'APPLICATION',
      phaseOrder: 1,
      description: 'Initial CV screening and application review',
      isActive: true,
      status: 'ACTIVE',
      passingScore: 70.00,
      projectId: project1.id
    }
  });

  const p1_phase2 = await prisma.phase.create({
    data: {
      name: 'Technical Assessment',
      type: 'ASSESSMENT',
      phaseOrder: 2,
      description: 'Coding challenge and technical evaluation',
      isActive: true,
      status: 'PENDING',
      passingScore: 75.00,
      projectId: project1.id
    }
  });

  const p1_phase3 = await prisma.phase.create({
    data: {
      name: 'Technical Interview',
      type: 'INTERVIEW',
      phaseOrder: 3,
      description: 'Live coding session and technical discussion',
      isActive: true,
      status: 'PENDING',
      passingScore: 80.00,
      projectId: project1.id
    }
  });

  const p1_phase4 = await prisma.phase.create({
    data: {
      name: 'Final Review',
      type: 'FINAL_REVIEW',
      phaseOrder: 4,
      description: 'Cultural fit and final decision',
      isActive: true,
      status: 'PENDING',
      passingScore: 75.00,
      projectId: project1.id
    }
  });

  // Project 2 Phases (AI/ML Engineer)
  const p2_phase1 = await prisma.phase.create({
    data: {
      name: 'Application Screening',
      type: 'SCREENING',
      phaseOrder: 1,
      description: 'CV review and initial qualification check',
      isActive: true,
      status: 'ACTIVE',
      passingScore: 75.00,
      projectId: project2.id
    }
  });

  const p2_phase2 = await prisma.phase.create({
    data: {
      name: 'ML Portfolio Review',
      type: 'ASSESSMENT',
      phaseOrder: 2,
      description: 'Review of machine learning projects and portfolio',
      isActive: true,
      status: 'PENDING',
      passingScore: 80.00,
      projectId: project2.id
    }
  });

  const p2_phase3 = await prisma.phase.create({
    data: {
      name: 'Technical Interview',
      type: 'INTERVIEW',
      phaseOrder: 3,
      description: 'Deep dive into ML concepts and problem-solving',
      isActive: true,
      status: 'PENDING',
      passingScore: 85.00,
      projectId: project2.id
    }
  });

  // Project 3 Phases (DevOps Engineer)
  const p3_phase1 = await prisma.phase.create({
    data: {
      name: 'Initial Screening',
      type: 'SCREENING',
      phaseOrder: 1,
      description: 'Basic qualification and experience review',
      isActive: true,
      status: 'ACTIVE',
      passingScore: 65.00,
      projectId: project3.id
    }
  });

  const p3_phase2 = await prisma.phase.create({
    data: {
      name: 'Infrastructure Assessment',
      type: 'ASSESSMENT',
      phaseOrder: 2,
      description: 'Hands-on infrastructure and deployment challenge',
      isActive: true,
      status: 'PENDING',
      passingScore: 75.00,
      projectId: project3.id
    }
  });

  console.log('👥 Creating sample candidates...');

  // Sample candidates for Project 1 (Full-Stack Developer)
  const candidate1 = await prisma.candidate.create({
    data: {
      name: 'Alice Johnson',
      email: '<EMAIL>',
      phone: '******-0101',
      city: 'San Francisco, CA',
      currentPhaseId: p1_phase1.id,
      currentStatus: 'IN_REVIEW',
      parsedProfile: {
        personal_info: {
          name: 'Alice Johnson',
          email: '<EMAIL>',
          phone: '******-0101',
          location: 'San Francisco, CA',
          summary: 'Experienced full-stack developer with 6 years of experience building scalable web applications using React, Node.js, and cloud technologies.'
        },
        education: [
          {
            degree: 'Bachelor of Science in Computer Science',
            institution: 'UC Berkeley',
            graduationYear: 2018,
            field: 'Computer Science',
            gpa: '3.7'
          }
        ],
        experience: [
          {
            title: 'Senior Full-Stack Developer',
            company: 'TechStartup Inc.',
            startDate: '2021-03-01',
            endDate: '2024-01-01',
            description: 'Led development of microservices architecture, built React applications, implemented CI/CD pipelines',
            location: 'San Francisco, CA',
            duration: '2y 10m'
          },
          {
            title: 'Full-Stack Developer',
            company: 'WebSolutions LLC',
            startDate: '2018-07-01',
            endDate: '2021-02-28',
            description: 'Developed e-commerce platforms using MERN stack, optimized database performance',
            location: 'San Francisco, CA',
            duration: '2y 8m'
          }
        ],
        skills: [
          'JavaScript', 'TypeScript', 'React', 'Node.js', 'Express.js', 'PostgreSQL',
          'MongoDB', 'Docker', 'AWS', 'GraphQL', 'Redis', 'Git', 'Jest'
        ],
        languages: ['English', 'Spanish'],
        certifications: ['AWS Certified Developer', 'React Professional Certificate']
      }
    }
  });

  const candidate2 = await prisma.candidate.create({
    data: {
      name: 'Bob Chen',
      email: '<EMAIL>',
      phone: '******-0102',
      city: 'Seattle, WA',
      currentPhaseId: p1_phase1.id,
      currentStatus: 'APPLIED',
      parsedProfile: {
        personal_info: {
          name: 'Bob Chen',
          email: '<EMAIL>',
          phone: '******-0102',
          location: 'Seattle, WA',
          summary: 'Frontend-focused developer with strong React skills and growing backend experience. Passionate about user experience and performance optimization.'
        },
        education: [
          {
            degree: 'Bachelor of Arts in Computer Science',
            institution: 'University of Washington',
            graduationYear: 2020,
            field: 'Computer Science',
            gpa: '3.5'
          }
        ],
        experience: [
          {
            title: 'Frontend Developer',
            company: 'DesignTech Co.',
            startDate: '2020-08-01',
            endDate: '2024-01-01',
            description: 'Built responsive web applications with React, implemented design systems, optimized performance',
            location: 'Seattle, WA',
            duration: '3y 5m'
          }
        ],
        skills: [
          'JavaScript', 'React', 'HTML5', 'CSS3', 'Sass', 'Webpack', 'Node.js',
          'Express.js', 'MySQL', 'Git', 'Figma'
        ],
        languages: ['English', 'Mandarin'],
        certifications: ['Google UX Design Certificate']
      }
    }
  });

  const candidate3 = await prisma.candidate.create({
    data: {
      name: 'Carol Martinez',
      email: '<EMAIL>',
      phone: '******-0103',
      city: 'Austin, TX',
      currentPhaseId: p1_phase1.id,
      currentStatus: 'APPLIED',
      parsedProfile: {
        personal_info: {
          name: 'Carol Martinez',
          email: '<EMAIL>',
          phone: '******-0103',
          location: 'Austin, TX',
          summary: 'Full-stack engineer with expertise in modern JavaScript frameworks and cloud architecture. Strong background in agile development and team leadership.'
        },
        education: [
          {
            degree: 'Master of Science in Software Engineering',
            institution: 'UT Austin',
            graduationYear: 2019,
            field: 'Software Engineering',
            gpa: '3.9'
          },
          {
            degree: 'Bachelor of Science in Mathematics',
            institution: 'Rice University',
            graduationYear: 2017,
            field: 'Mathematics',
            gpa: '3.8'
          }
        ],
        experience: [
          {
            title: 'Lead Full-Stack Engineer',
            company: 'CloudNative Solutions',
            startDate: '2022-01-01',
            endDate: '2024-01-01',
            description: 'Led team of 5 developers, architected microservices on AWS, implemented DevOps practices',
            location: 'Austin, TX',
            duration: '2y'
          },
          {
            title: 'Software Engineer',
            company: 'Enterprise Corp',
            startDate: '2019-06-01',
            endDate: '2021-12-31',
            description: 'Developed enterprise applications using React and Spring Boot, mentored junior developers',
            location: 'Austin, TX',
            duration: '2y 7m'
          }
        ],
        skills: [
          'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Node.js', 'Python', 'Java',
          'PostgreSQL', 'MongoDB', 'Docker', 'Kubernetes', 'AWS', 'Terraform', 'Git'
        ],
        languages: ['English', 'Spanish', 'Portuguese'],
        certifications: ['AWS Solutions Architect', 'Certified Kubernetes Administrator', 'Scrum Master']
      }
    }
  });

  // Sample candidates for Project 2 (AI/ML Engineer)
  const candidate4 = await prisma.candidate.create({
    data: {
      name: 'David Kim',
      email: '<EMAIL>',
      phone: '******-0201',
      city: 'New York, NY',
      currentPhaseId: p2_phase1.id,
      currentStatus: 'IN_REVIEW',
      parsedProfile: {
        personal_info: {
          name: 'David Kim',
          email: '<EMAIL>',
          phone: '******-0201',
          location: 'New York, NY',
          summary: 'Machine Learning Engineer with 5 years of experience in developing and deploying ML models for production systems. PhD in Computer Science with focus on Deep Learning.'
        },
        education: [
          {
            degree: 'PhD in Computer Science',
            institution: 'MIT',
            graduationYear: 2020,
            field: 'Machine Learning',
            gpa: '3.9'
          },
          {
            degree: 'Master of Science in Computer Science',
            institution: 'Stanford University',
            graduationYear: 2017,
            field: 'Artificial Intelligence',
            gpa: '3.8'
          }
        ],
        experience: [
          {
            title: 'Senior ML Engineer',
            company: 'AI Innovations Inc.',
            startDate: '2020-09-01',
            endDate: '2024-01-01',
            description: 'Developed NLP models for text analysis, implemented MLOps pipelines, led research initiatives',
            location: 'New York, NY',
            duration: '3y 4m'
          },
          {
            title: 'Research Scientist Intern',
            company: 'Google Research',
            startDate: '2019-06-01',
            endDate: '2019-08-31',
            description: 'Researched novel deep learning architectures for computer vision tasks',
            location: 'Mountain View, CA',
            duration: '3m'
          }
        ],
        skills: [
          'Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'Pandas', 'NumPy',
          'Jupyter', 'Docker', 'Kubernetes', 'AWS', 'GCP', 'MLflow', 'Apache Spark'
        ],
        languages: ['English', 'Korean'],
        certifications: ['Google Cloud ML Engineer', 'AWS ML Specialty', 'TensorFlow Developer']
      }
    }
  });

  const candidate5 = await prisma.candidate.create({
    data: {
      name: 'Emma Wilson',
      email: '<EMAIL>',
      phone: '******-0202',
      city: 'Boston, MA',
      currentPhaseId: p2_phase1.id,
      currentStatus: 'APPLIED',
      parsedProfile: {
        personal_info: {
          name: 'Emma Wilson',
          email: '<EMAIL>',
          phone: '******-0202',
          location: 'Boston, MA',
          summary: 'Data Scientist transitioning to ML Engineering with strong statistical background and growing expertise in production ML systems.'
        },
        education: [
          {
            degree: 'Master of Science in Statistics',
            institution: 'Harvard University',
            graduationYear: 2021,
            field: 'Applied Statistics',
            gpa: '3.7'
          },
          {
            degree: 'Bachelor of Science in Mathematics',
            institution: 'Boston University',
            graduationYear: 2019,
            field: 'Mathematics',
            gpa: '3.6'
          }
        ],
        experience: [
          {
            title: 'Data Scientist',
            company: 'FinTech Analytics',
            startDate: '2021-07-01',
            endDate: '2024-01-01',
            description: 'Built predictive models for financial risk assessment, performed statistical analysis, created data visualizations',
            location: 'Boston, MA',
            duration: '2y 6m'
          }
        ],
        skills: [
          'Python', 'R', 'SQL', 'Pandas', 'NumPy', 'Scikit-learn', 'Matplotlib',
          'Seaborn', 'Jupyter', 'Git', 'PostgreSQL', 'Tableau'
        ],
        languages: ['English', 'French'],
        certifications: ['IBM Data Science Professional', 'Coursera ML Specialization']
      }
    }
  });

  // Sample candidates for Project 3 (DevOps Engineer)
  const candidate6 = await prisma.candidate.create({
    data: {
      name: 'Frank Rodriguez',
      email: '<EMAIL>',
      phone: '******-0301',
      city: 'Austin, TX',
      currentPhaseId: p3_phase1.id,
      currentStatus: 'IN_REVIEW',
      parsedProfile: {
        personal_info: {
          name: 'Frank Rodriguez',
          email: '<EMAIL>',
          phone: '******-0301',
          location: 'Austin, TX',
          summary: 'DevOps Engineer with 4 years of experience in cloud infrastructure, containerization, and CI/CD pipeline implementation. Strong background in automation and monitoring.'
        },
        education: [
          {
            degree: 'Bachelor of Science in Information Technology',
            institution: 'Texas A&M University',
            graduationYear: 2020,
            field: 'Information Technology',
            gpa: '3.4'
          }
        ],
        experience: [
          {
            title: 'DevOps Engineer',
            company: 'CloudOps Solutions',
            startDate: '2021-01-01',
            endDate: '2024-01-01',
            description: 'Managed AWS infrastructure, implemented Kubernetes clusters, automated deployment pipelines',
            location: 'Austin, TX',
            duration: '3y'
          },
          {
            title: 'System Administrator',
            company: 'TechSupport Inc.',
            startDate: '2020-06-01',
            endDate: '2020-12-31',
            description: 'Maintained Linux servers, implemented monitoring solutions, managed network infrastructure',
            location: 'Austin, TX',
            duration: '7m'
          }
        ],
        skills: [
          'Docker', 'Kubernetes', 'AWS', 'Terraform', 'Ansible', 'Jenkins',
          'Linux', 'Bash', 'Python', 'Git', 'Prometheus', 'Grafana', 'Nginx'
        ],
        languages: ['English', 'Spanish'],
        certifications: ['AWS Solutions Architect', 'Certified Kubernetes Administrator', 'Docker Certified Associate']
      }
    }
  });

  const candidate7 = await prisma.candidate.create({
    data: {
      name: 'Grace Lee',
      email: '<EMAIL>',
      phone: '******-0302',
      city: 'Denver, CO',
      currentPhaseId: p3_phase1.id,
      currentStatus: 'APPLIED',
      parsedProfile: {
        personal_info: {
          name: 'Grace Lee',
          email: '<EMAIL>',
          phone: '******-0302',
          location: 'Denver, CO',
          summary: 'Infrastructure Engineer with strong background in cloud platforms and automation. Experienced in building scalable and reliable systems.'
        },
        education: [
          {
            degree: 'Bachelor of Science in Computer Engineering',
            institution: 'University of Colorado Boulder',
            graduationYear: 2021,
            field: 'Computer Engineering',
            gpa: '3.6'
          }
        ],
        experience: [
          {
            title: 'Infrastructure Engineer',
            company: 'ScaleUp Technologies',
            startDate: '2021-08-01',
            endDate: '2024-01-01',
            description: 'Built and maintained cloud infrastructure on Azure, implemented Infrastructure as Code, optimized costs',
            location: 'Denver, CO',
            duration: '2y 5m'
          }
        ],
        skills: [
          'Azure', 'Terraform', 'Docker', 'Kubernetes', 'Python', 'PowerShell',
          'Linux', 'Windows Server', 'Git', 'Azure DevOps', 'Monitoring'
        ],
        languages: ['English', 'Korean'],
        certifications: ['Azure Solutions Architect', 'Terraform Associate']
      }
    }
  });

  // Create some sample phase scores for demonstration
  console.log('📊 Creating sample phase scores...');

  // Score for Alice Johnson (excellent candidate)
  await prisma.phaseScore.create({
    data: {
      candidateId: candidate1.id,
      phaseId: p1_phase1.id,
      score: 88.5,
      feedback: {
        scores: {
          education_relevance: { score: 85, reasoning: 'Strong CS degree from UC Berkeley with relevant coursework' },
          skills_match: { score: 95, reasoning: 'Excellent match with all required skills and many preferred ones' },
          experience_quality: { score: 90, reasoning: 'Relevant full-stack experience with leadership responsibilities' },
          technical_proficiency: { score: 88, reasoning: 'Strong technical skills across the full stack' },
          career_progression: { score: 85, reasoning: 'Clear career advancement from developer to senior developer' },
          language_fit: { score: 90, reasoning: 'Native English speaker with additional Spanish skills' }
        },
        final_score: 88.5,
        recommendation: 'Highly Recommended',
        confidence_level: 'High',
        flags: ['excellent_skills_match', 'strong_experience', 'good_career_progression'],
        scored_at: new Date().toISOString()
      },
      evaluatedBy: 'AI_SYSTEM',
      evaluatedAt: new Date()
    }
  });

  // Score for David Kim (excellent ML candidate)
  await prisma.phaseScore.create({
    data: {
      candidateId: candidate4.id,
      phaseId: p2_phase1.id,
      score: 92.0,
      feedback: {
        scores: {
          education_relevance: { score: 98, reasoning: 'PhD from MIT in ML with excellent academic background' },
          skills_match: { score: 95, reasoning: 'Perfect match with all required ML skills and frameworks' },
          experience_quality: { score: 90, reasoning: 'Strong industry experience with research background' },
          technical_proficiency: { score: 95, reasoning: 'Expert-level technical skills in ML/AI' },
          career_progression: { score: 88, reasoning: 'Excellent progression from research to industry leadership' },
          language_fit: { score: 85, reasoning: 'Native English proficiency' }
        },
        final_score: 92.0,
        recommendation: 'Highly Recommended',
        confidence_level: 'High',
        flags: ['excellent_skills_match', 'strong_experience', 'high_technical_proficiency', 'good_career_progression'],
        scored_at: new Date().toISOString()
      },
      evaluatedBy: 'AI_SYSTEM',
      evaluatedAt: new Date()
    }
  });

  // Score for Bob Chen (good but junior candidate)
  await prisma.phaseScore.create({
    data: {
      candidateId: candidate2.id,
      phaseId: p1_phase1.id,
      score: 72.5,
      feedback: {
        scores: {
          education_relevance: { score: 80, reasoning: 'Good CS degree from University of Washington' },
          skills_match: { score: 75, reasoning: 'Strong frontend skills but limited backend experience' },
          experience_quality: { score: 70, reasoning: 'Good experience but primarily frontend focused' },
          technical_proficiency: { score: 75, reasoning: 'Strong in React and frontend, growing backend skills' },
          career_progression: { score: 65, reasoning: 'Limited career progression, still in first role' },
          language_fit: { score: 85, reasoning: 'Bilingual with strong English skills' }
        },
        final_score: 72.5,
        recommendation: 'Consider',
        confidence_level: 'Medium',
        flags: ['frontend_specialist', 'junior_level'],
        scored_at: new Date().toISOString()
      },
      evaluatedBy: 'AI_SYSTEM',
      evaluatedAt: new Date()
    }
  });

  // Create some interview templates
  console.log('🎤 Creating interview templates...');

  await prisma.interviewTemplate.create({
    data: {
      phaseId: p1_phase3.id,
      name: 'Full-Stack Technical Interview',
      description: 'Comprehensive technical interview covering frontend, backend, and system design',
      template: {
        sections: [
          {
            name: 'Frontend Development',
            questions: [
              'Explain the React component lifecycle',
              'How do you handle state management in large React applications?',
              'What are the differences between controlled and uncontrolled components?'
            ],
            duration: 20
          },
          {
            name: 'Backend Development',
            questions: [
              'Design a RESTful API for a blog application',
              'How do you handle authentication and authorization?',
              'Explain database indexing and when to use it'
            ],
            duration: 20
          },
          {
            name: 'System Design',
            questions: [
              'Design a URL shortening service like bit.ly',
              'How would you scale a web application to handle millions of users?'
            ],
            duration: 15
          },
          {
            name: 'Coding Challenge',
            questions: [
              'Implement a function to find the longest palindromic substring'
            ],
            duration: 15
          }
        ]
      },
      durationMinutes: 90
    }
  });

  await prisma.interviewTemplate.create({
    data: {
      phaseId: p2_phase3.id,
      name: 'ML Engineering Interview',
      description: 'Technical interview focusing on machine learning concepts and implementation',
      template: {
        sections: [
          {
            name: 'ML Fundamentals',
            questions: [
              'Explain the bias-variance tradeoff',
              'When would you use a random forest vs a neural network?',
              'How do you handle overfitting in machine learning models?'
            ],
            duration: 25
          },
          {
            name: 'Deep Learning',
            questions: [
              'Explain backpropagation in neural networks',
              'What are the differences between CNN and RNN architectures?',
              'How do you choose the right activation function?'
            ],
            duration: 25
          },
          {
            name: 'MLOps and Production',
            questions: [
              'How do you deploy ML models to production?',
              'Explain model versioning and monitoring strategies',
              'How do you handle data drift in production models?'
            ],
            duration: 20
          },
          {
            name: 'Practical Problem',
            questions: [
              'Design an ML system for fraud detection in financial transactions'
            ],
            duration: 30
          }
        ]
      },
      durationMinutes: 100
    }
  });

  console.log('✅ Database seeding completed successfully!');
  console.log('\n📊 Summary:');
  console.log(`   • Created 3 projects with job offers`);
  console.log(`   • Created 9 recruitment phases across projects`);
  console.log(`   • Created 7 sample candidates with detailed profiles`);
  console.log(`   • Created 3 sample phase scores for testing`);
  console.log(`   • Created 2 interview templates`);
  console.log('\n🎯 Test Scenarios Available:');
  console.log(`   1. Full-Stack Developer position with 3 candidates (various skill levels)`);
  console.log(`   2. AI/ML Engineer position with 2 candidates (expert vs transitioning)`);
  console.log(`   3. DevOps Engineer position with 2 candidates (different cloud platforms)`);
  console.log('\n🚀 Ready for testing the scoring system!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });