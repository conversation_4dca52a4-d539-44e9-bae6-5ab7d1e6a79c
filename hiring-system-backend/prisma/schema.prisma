// Recruitment Management System Prisma Schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  id          Int       @id @default(autoincrement())
  name        String    @db.VarChar(255)
  description String?   @db.Text
  startDate   DateTime? @map("start_date") @db.Date
  endDate     DateTime? @map("end_date") @db.Date
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  fileUploads FileUpload[]
  jobOffer    JobOffer?
  phases      Phase[]

  @@map("projects")
}

enum UploadPurpose {
  CV                   @map("cv")
  PROJECT_DESCRIPTION  @map("project_description")
  OTHER               @map("other")

  @@map("upload_purpose")
}

model FileUpload {
  id            Int           @id @default(autoincrement())
  fileName      String        @map("file_name") @db.VarChar(255)
  filePath      String        @map("file_path") @db.VarChar(500)
  fileSize      BigInt?       @map("file_size")
  uploadPurpose UploadPurpose @map("upload_purpose")
  projectId     Int?          @map("project_id")
  createdAt     DateTime      @default(now()) @map("created_at")

  // Relations
  project Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([uploadPurpose])
  @@map("file_uploads")
}

enum EmploymentType {
  FULL_TIME  @map("full_time")
  PART_TIME  @map("part_time")
  CONTRACT   @map("contract")
  INTERNSHIP @map("internship")

  @@map("employment_type")
}

enum ExperienceLevel {
  ENTRY     @map("entry")
  JUNIOR    @map("junior")
  MID       @map("mid")
  SENIOR    @map("senior")
  EXECUTIVE @map("executive")

  @@map("experience_level")
}

model JobOffer {
  id                  Int             @id @default(autoincrement())
  title               String          @db.VarChar(255)
  description         String?         @db.Text
  location            String?         @db.VarChar(255)
  salary              String?         @db.VarChar(100)
  employmentType      EmploymentType  @default(FULL_TIME) @map("employment_type")
  department          String?         @db.VarChar(100)
  experienceLevel     ExperienceLevel @default(MID) @map("experience_level")
  requiredSkills      String?         @map("required_skills") @db.Text
  applicationDeadline DateTime?       @map("application_deadline") @db.Date
  benefits            String?         @db.Text
  projectId           Int             @unique @map("project_id")
  isActive            Boolean         @default(true) @map("is_active")
  createdAt           DateTime        @default(now()) @map("created_at")
  updatedAt           DateTime        @updatedAt @map("updated_at")

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([isActive])
  @@index([applicationDeadline])
  @@map("job_offers")
}

enum PhaseType {
  APPLICATION   @map("application")
  SCREENING     @map("screening")
  INTERVIEW     @map("interview")
  ASSESSMENT    @map("assessment")
  FINAL_REVIEW  @map("final_review")

  @@map("phase_type")
}

enum PhaseStatus {
  PENDING   @map("pending")
  ACTIVE    @map("active")
  COMPLETED @map("completed")
  CANCELLED @map("cancelled")

  @@map("phase_status")
}

model Phase {
  id           Int         @id @default(autoincrement())
  name         String      @db.VarChar(255)
  type         PhaseType
  startDate    DateTime?   @map("start_date") @db.Date
  endDate      DateTime?   @map("end_date") @db.Date
  phaseOrder   Int         @map("phase_order")
  description  String?     @db.Text
  isActive     Boolean     @default(true) @map("is_active")
  status       PhaseStatus @default(PENDING)
  passingScore Decimal?    @map("passing_score") @db.Decimal(5, 2)
  projectId    Int         @map("project_id")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relations
  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  candidates        Candidate[]        @relation("CandidateCurrentPhase")
  phaseScores       PhaseScore[]
  interviewTemplate InterviewTemplate?

  @@unique([projectId, phaseOrder], name: "unique_project_phase_order")
  @@index([projectId, phaseOrder])
  @@index([status])
  @@map("phases")
}

enum CandidateStatus {
  APPLIED      @map("applied")
  IN_REVIEW    @map("in_review")
  INTERVIEWING @map("interviewing")
  ASSESSMENT   @map("assessment")
  HIRED        @map("hired")
  REJECTED     @map("rejected")
  WITHDRAWN    @map("withdrawn")

  @@map("candidate_status")
}

model Candidate {
  id               Int             @id @default(autoincrement())
  name             String          @db.VarChar(255)
  email            String          @unique @db.VarChar(255)
  phone            String?         @db.VarChar(50)
  city             String?         @db.VarChar(100)
  parsedProfile    Json?           @map("parsed_profile")
  cvFileUrl        String?         @map("cv_file_url") @db.VarChar(500)
  currentPhaseId   Int?            @map("current_phase_id")
  currentStatus    CandidateStatus @default(APPLIED) @map("current_status")
  applicationDate  DateTime        @default(now()) @map("application_date")
  updatedAt        DateTime        @updatedAt @map("updated_at")

  // Relations
  currentPhase Phase?       @relation("CandidateCurrentPhase", fields: [currentPhaseId], references: [id], onDelete: SetNull)
  phaseScores  PhaseScore[]
  interviews   Interview[]

  @@index([email])
  @@index([currentStatus])
  @@index([currentPhaseId])
  @@index([applicationDate])
  @@map("candidates")
}

model PhaseScore {
  id          Int      @id @default(autoincrement())
  candidateId Int      @map("candidate_id")
  phaseId     Int      @map("phase_id")
  score       Decimal? @db.Decimal(5, 2)
  feedback    Json?
  comment     String?  @db.Text
  evaluatedBy String?  @map("evaluated_by") @db.VarChar(255)
  evaluatedAt DateTime @default(now()) @map("evaluated_at")

  // Relations
  candidate Candidate @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  phase     Phase     @relation(fields: [phaseId], references: [id], onDelete: Cascade)

  @@unique([candidateId, phaseId], name: "unique_candidate_phase")
  @@index([phaseId])
  @@index([candidateId])
  @@map("phase_scores")
}

model InterviewTemplate {
  id              Int      @id @default(autoincrement())
  phaseId         Int      @unique @map("phase_id")
  name            String   @db.VarChar(255)
  description     String?  @db.Text
  template        Json
  durationMinutes Int      @default(60) @map("duration_minutes")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  phase      Phase       @relation(fields: [phaseId], references: [id], onDelete: Cascade)
  interviews Interview[]

  @@map("interview_templates")
}

enum InterviewStatus {
  SCHEDULED   @map("scheduled")
  IN_PROGRESS @map("in_progress")
  COMPLETED   @map("completed")
  CANCELLED   @map("cancelled")
  NO_SHOW     @map("no_show")

  @@map("interview_status")
}

model Interview {
  id                   Int               @id @default(autoincrement())
  candidateId          Int               @map("candidate_id")
  interviewTemplateId  Int               @map("interview_template_id")
  link                 String?           @db.VarChar(500)
  scheduledDate        DateTime?         @map("scheduled_date")
  actualStartTime      DateTime?         @map("actual_start_time")
  actualEndTime        DateTime?         @map("actual_end_time")
  responses            String?           @db.Text
  attachments          String?           @db.VarChar(500)
  status               InterviewStatus   @default(SCHEDULED)
  interviewerNotes     String?           @map("interviewer_notes") @db.Text
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")

  // Relations
  candidate         Candidate         @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  interviewTemplate InterviewTemplate @relation(fields: [interviewTemplateId], references: [id], onDelete: Cascade)

  @@index([candidateId])
  @@index([scheduledDate])
  @@index([status])
  @@map("interviews")
}

model WebhookEvent {
  id               Int      @id @default(autoincrement())

  // Event details
  eventType        String   @map("event_type") @db.VarChar(100)
  origin           String   @db.VarChar(100)
  message          String?  @db.Text

  // Profile information from HRFlow
  profileKey       String?  @map("profile_key") @db.VarChar(255)
  sourceKey        String?  @map("source_key") @db.VarChar(255)

  // Raw webhook data
  rawPayload       Json     @map("raw_payload")

  // Processing status
  processed        Boolean  @default(false)
  processingError  String?  @map("processing_error") @db.Text

  // Metadata
  receivedAt       DateTime @default(now()) @map("received_at")
  processedAt      DateTime? @map("processed_at")

  @@index([eventType])
  @@index([processed])
  @@index([receivedAt])
  @@map("webhook_events")
}