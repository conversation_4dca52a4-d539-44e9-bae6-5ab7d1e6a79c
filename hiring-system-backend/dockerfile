FROM node:20-alpine

WORKDIR /app

# Install dependencies first
COPY package*.json tsconfig*.json ./
COPY nodemon.json prisma ./
COPY tsoa.json ./

RUN npm install

# Install nodemon globally (optional, but cleaner)
RUN npm install -g nodemon

# Copy source
COPY src ./src

# Copy entrypoint
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Use entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]

# Expose app port
EXPOSE 4000

# Default command (overridden in docker-compose)
CMD ["nodemon"]
