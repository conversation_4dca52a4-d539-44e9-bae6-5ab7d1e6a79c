# Database
DATABASE_URL="postgresql://username:password@localhost:5432/hiring_system_db"

# HRFlow API Configuration
HRFLOW_API_KEY="your_hrflow_api_key_here"
HRFLOW_USER_EMAIL="your_hrflow_user_email_here"
HRFLOW_SOURCE_KEY="your_hrflow_source_key_here"

# OpenAI Configuration
OPENAI_API_KEY="your_openai_api_key_here"

# Azure OpenAI Configuration (optional - only if using Azure OpenAI)
AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
AZURE_OPENAI_API_VERSION="2024-02-15-preview"
AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4"

# Application Configuration
NODE_ENV="development"
PORT=3000

# File Upload Configuration
MAX_FILE_SIZE_MB=10
UPLOAD_DIR="./uploads"
