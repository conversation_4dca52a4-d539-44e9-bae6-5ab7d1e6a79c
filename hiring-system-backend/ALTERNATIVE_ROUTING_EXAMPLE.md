# Alternative Manual Routing Approach

If you prefer manual routing instead of decorators, here's how to implement it:

## 1. Plain Controller (No Decorators)

```typescript
// src/controllers/project.controller.ts
import { injectable, inject } from "inversify";
import { Request, Response } from "express";
import { ProjectService } from "@/services/project.service";

@injectable()
export class ProjectController {
  constructor(
    @inject("ProjectService") private projectService: ProjectService
  ) {}

  async getProjects(req: Request, res: Response): Promise<void> {
    try {
      const projects = await this.projectService.findAll();
      res.json(projects);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch projects" });
    }
  }

  async createProject(req: Request, res: Response): Promise<void> {
    try {
      const project = await this.projectService.create(req.body);
      res.status(201).json(project);
    } catch (error) {
      res.status(500).json({ error: "Failed to create project" });
    }
  }
}
```

## 2. Manual Router

```typescript
// src/routes/project.router.ts
import { Router } from "express";
import { Container } from "inversify";
import { ProjectController } from "@/controllers/project.controller";

export function createProjectRouter(container: Container): Router {
  const router = Router();
  const controller = container.get<ProjectController>("ProjectController");

  router.get("/", (req, res) => controller.getProjects(req, res));
  router.post("/", (req, res) => controller.createProject(req, res));
  router.get("/:id", (req, res) => controller.getProject(req, res));
  router.put("/:id", (req, res) => controller.updateProject(req, res));
  router.delete("/:id", (req, res) => controller.deleteProject(req, res));

  return router;
}
```

## 3. Manual App Setup

```typescript
// src/app.ts
import express from 'express';
import { container } from '@/config/inversify.config';
import { createProjectRouter } from '@/routes/project.router';
import { createCandidateRouter } from '@/routes/candidate.router';

const app = express();

app.use(express.json());

// Manual route registration
app.use('/projects', createProjectRouter(container));
app.use('/candidates', createCandidateRouter(container));

export { app };
```

## 4. Container Binding

```typescript
// src/config/inversify.config.ts
container.bind<ProjectController>("ProjectController").to(ProjectController);
container.bind<ProjectService>("ProjectService").to(ProjectService);
```

## Pros and Cons

### Manual Routing:
✅ More explicit control over routes
✅ Easier to understand for developers familiar with Express
✅ More flexibility in middleware application
❌ More boilerplate code
❌ Manual route registration required

### Decorator Routing (Current):
✅ Less boilerplate code
✅ Automatic route registration
✅ Clean, declarative syntax
✅ Built-in dependency injection
❌ Less explicit
❌ Requires understanding of decorators
