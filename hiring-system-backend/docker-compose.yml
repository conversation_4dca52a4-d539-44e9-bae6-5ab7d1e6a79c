version: "3.9"

services:
  hiring-system-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hiring-system-api
    restart: always
    env_file:
      - .env
    ports:
      - "3000:3000"
    depends_on:
      - db
    volumes:
      - ./prisma:/app/prisma:Z
      - ./src:/app/src:Z


  db:
    image: postgres:15-alpine
    container_name: hiring-system-db
    restart: always
    ports:
      - "5532:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hiring_system
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
