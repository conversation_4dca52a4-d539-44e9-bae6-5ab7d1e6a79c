/**
 * Development debugging utilities
 */

export class DebugLogger {
  private static isDevelopment = process.env.NODE_ENV === 'development';

  /**
   * Enhanced console.log with context
   */
  static log(category: string, message: string, data?: any): void {
    if (!this.isDevelopment) return;

    const timestamp = new Date().toISOString();
    const emoji = this.getCategoryEmoji(category);
    
    console.log(`${emoji} [${category.toUpperCase()}] ${timestamp}`);
    console.log(`   ${message}`);
    
    if (data !== undefined) {
      if (typeof data === 'object') {
        console.log('   Data:', JSON.stringify(data, null, 2));
      } else {
        console.log('   Data:', data);
      }
    }
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }

  /**
   * Error logging with full context
   */
  static error(category: string, error: any, context?: any): void {
    const timestamp = new Date().toISOString();
    
    console.error('🚨 ERROR DETECTED:');
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.error(`📅 Timestamp: ${timestamp}`);
    console.error(`🏷️  Category: ${category}`);
    
    if (error instanceof Error) {
      console.error(`❌ Error Name: ${error.name}`);
      console.error(`💬 Error Message: ${error.message}`);
      console.error('📚 Stack Trace:');
      console.error(error.stack);
    } else {
      console.error(`❌ Error: ${JSON.stringify(error, null, 2)}`);
    }
    
    if (context) {
      console.error('🔍 Context:');
      console.error(JSON.stringify(context, null, 2));
    }
    
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }

  /**
   * Performance timing
   */
  static time(label: string): void {
    if (!this.isDevelopment) return;
    console.time(`⏱️  ${label}`);
  }

  static timeEnd(label: string): void {
    if (!this.isDevelopment) return;
    console.timeEnd(`⏱️  ${label}`);
  }

  /**
   * Database query logging
   */
  static dbQuery(query: string, params?: any, duration?: number): void {
    if (!this.isDevelopment) return;

    console.log('🗄️  DATABASE QUERY:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`🔍 Query: ${query}`);
    
    if (params) {
      console.log(`📋 Parameters: ${JSON.stringify(params, null, 2)}`);
    }
    
    if (duration !== undefined) {
      console.log(`⏱️  Duration: ${duration}ms`);
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }

  /**
   * API call logging
   */
  static apiCall(method: string, url: string, status?: number, duration?: number, data?: any): void {
    if (!this.isDevelopment) return;

    const statusEmoji = status ? (status < 400 ? '✅' : '❌') : '🔄';
    
    console.log(`${statusEmoji} API CALL:`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`🔗 ${method} ${url}`);
    
    if (status) {
      console.log(`📊 Status: ${status}`);
    }
    
    if (duration !== undefined) {
      console.log(`⏱️  Duration: ${duration}ms`);
    }
    
    if (data) {
      console.log(`📦 Data: ${JSON.stringify(data, null, 2)}`);
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }

  /**
   * Memory usage logging
   */
  static memory(label?: string): void {
    if (!this.isDevelopment) return;

    const memUsage = process.memoryUsage();
    const prefix = label ? `${label} - ` : '';
    
    console.log(`📊 ${prefix}MEMORY USAGE:`);
    console.log(`   RSS: ${(memUsage.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   External: ${(memUsage.external / 1024 / 1024).toFixed(2)} MB`);
  }

  /**
   * Object inspection with circular reference handling
   */
  static inspect(obj: any, label?: string): void {
    if (!this.isDevelopment) return;

    const prefix = label ? `${label}: ` : '';
    
    try {
      console.log(`🔍 ${prefix}OBJECT INSPECTION:`);
      console.log(JSON.stringify(obj, this.circularReplacer(), 2));
    } catch (error) {
      console.log(`🔍 ${prefix}OBJECT INSPECTION (with circular refs):`);
      console.log(obj);
    }
  }

  /**
   * Get emoji for category
   */
  private static getCategoryEmoji(category: string): string {
    const emojiMap: { [key: string]: string } = {
      'database': '🗄️',
      'api': '🌐',
      'auth': '🔐',
      'validation': '✅',
      'service': '⚙️',
      'controller': '🎮',
      'middleware': '🔗',
      'config': '⚙️',
      'startup': '🚀',
      'shutdown': '🛑',
      'performance': '⚡',
      'security': '🛡️',
      'file': '📁',
      'network': '🌐',
      'cache': '💾',
      'queue': '📋',
      'email': '📧',
      'payment': '💳',
      'user': '👤',
      'admin': '👑',
      'debug': '🐛',
      'info': 'ℹ️',
      'warning': '⚠️',
      'success': '✅',
      'error': '❌'
    };

    return emojiMap[category.toLowerCase()] || '📝';
  }

  /**
   * Circular reference replacer for JSON.stringify
   */
  private static circularReplacer() {
    const seen = new WeakSet();
    return (key: string, value: any) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    };
  }
}

/**
 * Quick debug functions for common use cases
 */
export const debug = {
  log: (message: string, data?: any) => DebugLogger.log('debug', message, data),
  error: (error: any, context?: any) => DebugLogger.error('error', error, context),
  api: (method: string, url: string, status?: number, duration?: number) => 
    DebugLogger.apiCall(method, url, status, duration),
  db: (query: string, params?: any, duration?: number) => 
    DebugLogger.dbQuery(query, params, duration),
  memory: (label?: string) => DebugLogger.memory(label),
  inspect: (obj: any, label?: string) => DebugLogger.inspect(obj, label),
  time: (label: string) => DebugLogger.time(label),
  timeEnd: (label: string) => DebugLogger.timeEnd(label)
};
