import { CreateCandidateDTO, UpdateCandidateDTO, CandidateResponseDTO } from "@/dto/candidate.dto";

export interface ICandidateService {
  create(data: CreateCandidateDTO): Promise<CandidateResponseDTO>;
  findAll(): Promise<CandidateResponseDTO[]>;
  findById(id: number): Promise<CandidateResponseDTO | null>;
  findByProject(projectId: number): Promise<CandidateResponseDTO[]>;
  update(id: number, data: UpdateCandidateDTO): Promise<CandidateResponseDTO | null>;
  delete(id: number): Promise<boolean>;
  uploadAndParseCV(
    projectId: number, 
    fileBuffer: Buffer, 
    filename: string, 
    autoScore?: boolean
  ): Promise<CandidateResponseDTO>;
  scoreCandidate(candidateId: number, phaseId: number): Promise<void>;
  scoreAllCandidates(projectId: number): Promise<{ success: boolean; scored_count: number; failed_count: number }>;
}
