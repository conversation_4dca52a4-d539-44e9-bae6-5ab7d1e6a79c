import { 
  HRFlowWebhookPayload, 
  WebhookEventCreate, 
  WebhookEventResponse, 
  WebhookProcessingResult 
} from '@/dto/webhook.dto';

export interface IWebhookService {
  /**
   * Verify webhook signature from HRFlow
   */
  verifyWebhookSignature(payload: string, signature: string): boolean;

  /**
   * Parse webhook form data into structured payload
   */
  parseWebhookPayload(formData: Record<string, any>): HRFlowWebhookPayload;

  /**
   * Process incoming webhook
   */
  processWebhook(payload: HRFlowWebhookPayload, rawPayload: Record<string, any>): Promise<WebhookProcessingResult>;

  /**
   * Create webhook event record
   */
  createWebhookEvent(payload: HRFlowWebhookPayload, rawPayload: Record<string, any>): Promise<WebhookEventResponse>;

  /**
   * Handle profile success events
   */
  handleProfileSuccess(payload: HRFlowWebhookPayload): Promise<void>;
}
