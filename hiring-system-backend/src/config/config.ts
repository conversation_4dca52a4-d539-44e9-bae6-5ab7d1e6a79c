import { config } from 'dotenv';

// Load environment variables
config();

export interface AppConfig {
  // Database
  databaseUrl: string;

  // HRFlow API
  hrflowApiKey: string;
  hrflowUserEmail: string;
  hrflowSourceKey: string;
  hrflowWebhookSecret?: string;

  // OpenAI
  openaiApiKey: string;

  // Azure OpenAI (optional)
  azureOpenaiEndpoint?: string;
  azureOpenaiApiVersion?: string;
  azureOpenaiDeploymentName?: string;

  // Application
  nodeEnv: string;
  port: number;

  // File Upload
  maxFileSizeMB: number;
  uploadDir: string;
}

export const appConfig: AppConfig = {
  // Database
  databaseUrl: process.env.DATABASE_URL || "postgresql://username:password@localhost:5432/hiring_system_db",

  // HRFlow API
  hrflowApiKey: process.env.HRFLOW_API_KEY || "",
  hrflowUserEmail: process.env.HRFLOW_USER_EMAIL || "",
  hrflowSourceKey: process.env.HRFLOW_SOURCE_KEY || "",
  hrflowWebhookSecret: process.env.HRFLOW_WEBHOOK_SECRET,

  // OpenAI
  openaiApiKey: process.env.OPENAI_API_KEY || "",

  // Azure OpenAI (optional)
  azureOpenaiEndpoint: process.env.AZURE_OPENAI_ENDPOINT,
  azureOpenaiApiVersion: process.env.AZURE_OPENAI_API_VERSION,
  azureOpenaiDeploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,

  // Application
  nodeEnv: process.env.NODE_ENV || "development",
  port: parseInt(process.env.PORT || "3000"),

  // File Upload
  maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB || "10"),
  uploadDir: process.env.UPLOAD_DIR || "./uploads"
};

// Validation function
export function validateConfig(): void {
  const requiredEnvVars = [
    'DATABASE_URL',
    'HRFLOW_API_KEY',
    'HRFLOW_USER_EMAIL',
    'OPENAI_API_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    console.error('Please check your .env file and ensure all required variables are set.');
    process.exit(1);
  }
  
  console.log('✅ Configuration validated successfully');
}
