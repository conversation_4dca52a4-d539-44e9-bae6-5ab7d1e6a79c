import { Container, decorate, injectable } from "inversify";
import { buildProviderModule } from "inversify-binding-decorators";
import { Controller } from "tsoa";

// Create a new container tsoa can use
const container = new Container();

// Makes tsoa's Controller injectable
decorate(injectable(), Controller);

// Import services and controllers to ensure decorators are processed
import "@/services/project.service";
import "@/services/candidate.service";
import "@/services/hrflow.service";
import "@/services/scoring.service";
import "@/services/webhook.service";
import "@/controllers/inversify/project.controller";
import "@/controllers/inversify/candidate.controller";
import "@/controllers/inversify/webhook.controller";

// make inversify aware of inversify-binding-decorators
container.load(buildProviderModule());

export { container };