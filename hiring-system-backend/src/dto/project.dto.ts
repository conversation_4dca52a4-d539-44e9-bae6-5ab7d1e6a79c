/**
 * Data required to create a new project
 */
export interface CreateProjectDTO {
  /** Project name */
  name: string;
  /** Project description */
  description?: string;
  /** Project start date */
  startDate?: string;
  /** Project end date */
  endDate?: string;
}

/**
 * Data for updating an existing project
 */
export interface UpdateProjectDTO {
  /** Project name */
  name?: string;
  /** Project description */
  description?: string;
  /** Project start date */
  startDate?: string;
  /** Project end date */
  endDate?: string;
}

/**
 * Complete project information returned by API
 */
export interface ProjectResponseDTO {
  /** Unique project identifier */
  id: number;
  /** Project name */
  name: string;
  /** Project description */
  description?: string | null;
  /** Project start date */
  startDate?: string | null;
  /** Project end date */
  endDate?: string | null;
  /** Creation timestamp */
  createdAt: string;
  /** Last update timestamp */
  updatedAt: string;
}
  