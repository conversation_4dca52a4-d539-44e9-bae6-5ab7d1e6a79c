/**
 * DTOs for HRFlow webhook handling
 */

export interface HRFlowWebhookPayload {
  type: string;
  origin: string;
  message: string;
  profile: Record<string, any> | string;
  team_name?: string;
}

export interface HRFlowProfile {
  key: string;
  source?: {
    key: string;
  };
  info?: {
    full_name?: string;
    email?: string;
    phone?: string;
    location?: {
      text?: string;
    };
  };
  summary?: string;
  educations?: Array<{
    title?: string;
    school?: string;
    end_date?: string;
    description?: string;
    grade?: string;
  }>;
  experiences?: Array<{
    title?: string;
    company?: string;
    start_date?: string;
    end_date?: string;
    description?: string;
    location?: {
      text?: string;
    };
  }>;
  skills?: Array<{
    name?: string;
  } | string>;
  languages?: Array<{
    name?: string;
  } | string>;
  certifications?: Array<{
    name?: string;
  } | string>;
}

export interface WebhookEventCreate {
  eventType: string;
  origin: string;
  message: string;
  profileKey?: string;
  sourceKey?: string;
  rawPayload: Record<string, any>;
}

export interface WebhookEventResponse {
  id: number;
  eventType: string;
  origin: string;
  message: string;
  profileKey?: string;
  sourceKey?: string;
  processed: boolean;
  processingError?: string;
  receivedAt: Date;
  processedAt?: Date;
}

export interface WebhookProcessingResult {
  success: boolean;
  message: string;
  webhookEventId: number;
  candidateId?: number;
  error?: string;
}
