import axios from 'axios';
import { URLSearchParams } from 'url';

/**
 * Test script for HRFlow webhook endpoint
 * This simulates the webhook payload format that HRFlow sends
 */

const BASE_URL = 'http://localhost:3001';

// Sample webhook data that mimics HRFlow's format
const webhookData = {
  type: 'profile.parsing.success',
  origin: 'api',
  message: 'profile parsing succeed',
  profile: JSON.stringify({
    key: 'd821393853fc32b08c93b8d38590817c72048ec4',
    source: {
      key: 'd900ec70c67d43c71027f9bc63ec3b5b3e16c1d8'
    },
    info: {
      full_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      location: {
        text: 'New York, NY'
      }
    },
    summary: 'Experienced software developer with 5+ years in web development',
    experiences: [
      {
        title: 'Senior Developer',
        company: 'Tech Corp',
        start_date: '2020-01-01',
        end_date: '2024-01-01',
        description: 'Led development of web applications',
        location: {
          text: 'San Francisco, CA'
        }
      }
    ],
    skills: [
      { name: 'JavaScript' },
      { name: 'TypeScript' },
      { name: 'React' }
    ],
    educations: [
      {
        title: 'Computer Science',
        school: 'University of Technology',
        end_date: '2019-05-01',
        description: 'Bachelor of Science in Computer Science'
      }
    ]
  })
};

async function testWebhookEndpoint() {
  console.log('🧪 Testing HRFlow webhook endpoint...\n');

  try {
    // Test 1: Test endpoint
    console.log('1. Testing webhook test endpoint...');
    const testResponse = await axios.post(`${BASE_URL}/webhooks/test`);
    console.log('✅ Test endpoint response:', testResponse.data);
    console.log('');

    // Test 2: Debug endpoint
    console.log('2. Testing webhook debug endpoint...');
    const formData = new URLSearchParams(webhookData);
    
    const debugResponse = await axios.post(
      `${BASE_URL}/webhooks/debug`,
      formData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Signature': 'sha256=test_signature'
        }
      }
    );
    console.log('✅ Debug endpoint response:', JSON.stringify(debugResponse.data, null, 2));
    console.log('');

    // Test 3: Main HRFlow webhook endpoint
    console.log('3. Testing main HRFlow webhook endpoint...');
    const webhookResponse = await axios.post(
      `${BASE_URL}/webhooks/hrflow`,
      formData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Signature': 'sha256=test_signature'
        }
      }
    );
    console.log('✅ Webhook endpoint response:', JSON.stringify(webhookResponse.data, null, 2));
    console.log('');

    // Test 4: Test with different webhook types
    console.log('4. Testing different webhook types...');
    
    const webhookTypes = [
      'profile.storing.success',
      'profile.searching.success',
      'unknown.webhook.type'
    ];

    for (const type of webhookTypes) {
      console.log(`   Testing webhook type: ${type}`);
      const testData = { ...webhookData, type };
      const testFormData = new URLSearchParams(testData);
      
      const response = await axios.post(
        `${BASE_URL}/webhooks/hrflow`,
        testFormData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Signature': 'sha256=test_signature'
          }
        }
      );
      console.log(`   ✅ Response for ${type}:`, response.data.message);
    }

    console.log('\n🎉 All webhook tests completed successfully!');

  } catch (error: any) {
    if (error.response) {
      console.error('❌ Webhook test failed:');
      console.error('Status:', error.response?.status);
      console.error('Data:', error.response?.data);
      console.error('URL:', error.config?.url);
    } else {
      console.error('❌ Unexpected error:', error);
    }
  }
}

async function testWithoutServer() {
  console.log('⚠️  Server not running. Please start the server first:');
  console.log('   npm run dev');
  console.log('   or');
  console.log('   npm run dev:inversify');
}

// Check if server is running before testing
async function checkServerAndTest() {
  try {
    await axios.get(`${BASE_URL}/health`);
    await testWebhookEndpoint();
  } catch (error) {
    await testWithoutServer();
  }
}

// Run the test
checkServerAndTest();
