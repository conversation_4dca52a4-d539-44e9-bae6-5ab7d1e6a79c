import 'reflect-metadata';
import { container } from '@/config/inversify.config';
import { IScoringService } from '@/interfaces/IScoringService';
import { IHRFlowService } from '@/interfaces/IHRFlowService';
import { TYPES } from '@/config/types';
import { ParsedProfile } from '@/dto/candidate.dto';
import { JobRequirements, ScoringConfig } from '@/dto/scoring.dto';

async function testScoringSystem() {
  console.log('🧪 Testing Scoring System Implementation...\n');

  try {
    // Get services from container
    const scoringService = container.get<IScoringService>(TYPES.IScoringService);
    const hrflowService = container.get<IHRFlowService>(TYPES.IHRFlowService);

    // Test 1: HRFlow Connection
    console.log('1️⃣ Testing HRFlow Connection...');
    const hrflowConnected = await hrflowService.validateConnection();
    console.log(`   HRFlow Status: ${hrflowConnected ? '✅ Connected' : '❌ Failed'}\n`);

    // Test 2: Sample Candidate Data
    console.log('2️⃣ Testing Scoring with Sample Data...');
    
    const sampleCandidate: ParsedProfile = {
      personal_info: {
        name: "John Doe",
        email: "<EMAIL>",
        phone: "******-0123",
        location: "San Francisco, CA",
        summary: "Experienced software engineer with 5 years in full-stack development"
      },
      education: [
        {
          degree: "Bachelor of Science in Computer Science",
          institution: "Stanford University",
          graduationYear: 2018,
          field: "Computer Science",
          gpa: "3.8"
        }
      ],
      experience: [
        {
          title: "Senior Software Engineer",
          company: "TechCorp Inc.",
          startDate: "2020-01-01",
          endDate: "2024-01-01",
          description: "Led development of microservices architecture using Node.js and React",
          location: "San Francisco, CA",
          duration: "4y"
        },
        {
          title: "Software Engineer",
          company: "StartupXYZ",
          startDate: "2018-06-01",
          endDate: "2019-12-31",
          description: "Full-stack development with JavaScript, Python, and PostgreSQL",
          location: "San Francisco, CA",
          duration: "1y 6m"
        }
      ],
      skills: [
        "JavaScript", "TypeScript", "Node.js", "React", "Express.js",
        "PostgreSQL", "MongoDB", "Docker", "AWS", "Git"
      ],
      languages: ["English", "Spanish"],
      certifications: ["AWS Certified Developer", "Google Cloud Professional"]
    };

    const jobRequirements: JobRequirements = {
      required_education: "Bachelor's degree in Computer Science or related field",
      preferred_fields: ["Computer Science", "Software Engineering"],
      min_experience_years: 3,
      preferred_experience_years: 5,
      required_skills: ["JavaScript", "Node.js", "React"],
      preferred_skills: ["TypeScript", "Docker", "AWS", "PostgreSQL"],
      technical_requirements: {
        programming_languages: ["JavaScript", "TypeScript"],
        frameworks: ["React", "Express.js"],
        databases: ["PostgreSQL", "MongoDB"]
      },
      required_languages: ["English"],
      location_requirements: "San Francisco Bay Area",
      remote_work: true
    };

    const scoringConfig: ScoringConfig = {
      weights: {
        education_relevance: 0.20,
        skills_match: 0.30,
        experience_quality: 0.25,
        technical_proficiency: 0.15,
        career_progression: 0.07,
        language_fit: 0.03
      },
      passing_thresholds: {
        minimum_score: 60,
        recommended_score: 75
      }
    };

    console.log('   📊 Scoring candidate...');
    const scoringResult = await scoringService.scoreCandidate(
      sampleCandidate,
      jobRequirements,
      scoringConfig
    );

    console.log('   ✅ Scoring completed!\n');

    // Test 3: Display Results
    console.log('3️⃣ Scoring Results:');
    console.log(`   🎯 Final Score: ${scoringResult.final_score}/100`);
    console.log(`   📋 Recommendation: ${scoringResult.recommendation}`);
    console.log(`   🔍 Confidence: ${scoringResult.confidence_level}`);
    console.log(`   🏷️  Flags: ${scoringResult.flags.join(', ') || 'None'}\n`);

    console.log('4️⃣ Detailed Breakdown:');
    Object.entries(scoringResult.scores).forEach(([dimension, detail]) => {
      console.log(`   ${dimension.replace('_', ' ').toUpperCase()}: ${detail.score}/100`);
      console.log(`      Reasoning: ${detail.reasoning}\n`);
    });

    console.log('🎉 All tests completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Set up your .env file with API keys');
    console.log('   2. Run database migrations');
    console.log('   3. Start the server with npm run dev');
    console.log('   4. Test CV upload endpoints');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your .env file configuration');
    console.log('   2. Verify API keys are valid');
    console.log('   3. Ensure database is running');
    console.log('   4. Check network connectivity');
  }
}

// Run the test
testScoringSystem().catch(console.error);
