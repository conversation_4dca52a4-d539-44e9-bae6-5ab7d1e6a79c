import { inject } from "inversify";
import { TYPES } from "@/config/types";
import prisma from "@/config/prisma";
import { CreateCandidateDTO, UpdateCandidateDTO, CandidateResponseDTO, ParsedProfile } from "@/dto/candidate.dto";
import { JobRequirements, ScoringConfig } from "@/dto/scoring.dto";
import { IHRFlowService } from "@/interfaces/IHRFlowService";
import { IScoringService } from "@/interfaces/IScoringService";
import { ICandidateService } from "@/interfaces/ICandidateService";
import { provideSingleton } from "@/utils/provideSingleton";

@provideSingleton(TYPES.ICandidateService)
export class CandidateService implements ICandidateService {
  constructor(
    @inject(TYPES.IHRFlowService) private hrflowService: IHRFlowService,
    @inject(TYPES.IScoringService) private scoringService: IScoringService
  ) {}

  async create(data: CreateCandidateDTO): Promise<CandidateResponseDTO> {
    const candidate = await prisma.candidate.create({ 
      data: {
        ...data,
        currentStatus: 'APPLIED'
      }
    });
    return this.mapToResponseDTO(candidate);
  }

  async findAll(): Promise<CandidateResponseDTO[]> {
    const candidates = await prisma.candidate.findMany({
      include: {
        currentPhase: true,
        phaseScores: true
      }
    });
    return candidates.map(this.mapToResponseDTO);
  }

  async findById(id: number): Promise<CandidateResponseDTO | null> {
    const candidate = await prisma.candidate.findUnique({ 
      where: { id },
      include: {
        currentPhase: true,
        phaseScores: true
      }
    });
    return candidate ? this.mapToResponseDTO(candidate) : null;
  }

  async findByProject(projectId: number): Promise<CandidateResponseDTO[]> {
    // Get candidates through phases of the project
    const candidates = await prisma.candidate.findMany({
      where: {
        currentPhase: {
          projectId: projectId
        }
      },
      include: {
        currentPhase: true,
        phaseScores: {
          include: {
            phase: true
          }
        }
      }
    });
    return candidates.map(this.mapToResponseDTO);
  }

  async update(id: number, data: UpdateCandidateDTO): Promise<CandidateResponseDTO | null> {
    try {
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.phone !== undefined) updateData.phone = data.phone;
      if (data.city !== undefined) updateData.city = data.city;
      if (data.currentPhaseId !== undefined) updateData.currentPhaseId = data.currentPhaseId;
      if (data.currentStatus !== undefined) updateData.currentStatus = data.currentStatus;

      const candidate = await prisma.candidate.update({
        where: { id },
        data: updateData,
        include: {
          currentPhase: true,
          phaseScores: true
        }
      });
      return this.mapToResponseDTO(candidate);
    } catch (error) {
      return null;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      await prisma.candidate.delete({ where: { id } });
      return true;
    } catch (error) {
      return false;
    }
  }

  async uploadAndParseCV(
    projectId: number, 
    fileBuffer: Buffer, 
    filename: string, 
    autoScore: boolean = true
  ): Promise<CandidateResponseDTO> {
    try {
      console.log(`Processing CV upload: ${filename} for project ${projectId}`);

      // Parse CV with HRFlow
      const parsedProfile = await this.hrflowService.parseCV(fileBuffer, filename);
      
      if (!parsedProfile) {
        throw new Error("Failed to parse CV");
      }

      // Extract basic info from parsed profile
      const personalInfo = parsedProfile.personal_info || {};
      
      // Get the first phase of the project
      const firstPhase = await prisma.phase.findFirst({
        where: { projectId },
        orderBy: { phaseOrder: 'asc' }
      });

      if (!firstPhase) {
        throw new Error("No phases found for project");
      }

      // Create candidate
      const candidate = await prisma.candidate.create({
        data: {
          name: personalInfo.name || `Candidate from ${filename}`,
          email: personalInfo.email || `temp_${Date.now()}@example.com`,
          phone: personalInfo.phone,
          city: personalInfo.location,
          parsedProfile: parsedProfile as any,
          cvFileUrl: `/uploads/${filename}`, // You'll need to implement file storage
          currentPhaseId: firstPhase.id,
          currentStatus: 'APPLIED'
        },
        include: {
          currentPhase: true,
          phaseScores: true
        }
      });

      console.log(`Candidate created with ID: ${candidate.id}`);

      // Auto-score if enabled
      if (autoScore) {
        await this.scoreCandidate(candidate.id, firstPhase.id);
      }

      return this.mapToResponseDTO(candidate);

    } catch (error) {
      console.error("Error uploading and parsing CV:", error);
      throw error;
    }
  }

  async scoreCandidate(candidateId: number, phaseId: number): Promise<void> {
    try {
      console.log(`Starting scoring for candidate ${candidateId} in phase ${phaseId}`);

      // Get candidate with parsed profile
      const candidate = await prisma.candidate.findUnique({
        where: { id: candidateId },
        include: { currentPhase: { include: { project: { include: { jobOffer: true } } } } }
      });

      if (!candidate || !candidate.parsedProfile) {
        throw new Error("Candidate not found or no parsed profile available");
      }

      // Get job requirements from job offer
      const jobOffer = candidate.currentPhase?.project?.jobOffer;
      if (!jobOffer) {
        throw new Error("No job offer found for project");
      }

      // Parse job requirements from the requiredSkills field
      const jobRequirements: JobRequirements = this.parseJobRequirements(jobOffer);
      
      // Default scoring config
      const scoringConfig: ScoringConfig = {
        weights: {
          education_relevance: 0.25,
          skills_match: 0.25,
          experience_quality: 0.20,
          technical_proficiency: 0.15,
          career_progression: 0.10,
          language_fit: 0.05
        }
      };

      // Score the candidate
      const scoringResults = await this.scoringService.scoreCandidate(
        candidate.parsedProfile as ParsedProfile,
        jobRequirements,
        scoringConfig
      );

      // Save the score
      await prisma.phaseScore.upsert({
        where: {
          unique_candidate_phase: {
            candidateId: candidateId,
            phaseId: phaseId
          }
        },
        update: {
          score: scoringResults.final_score,
          feedback: scoringResults as any,
          evaluatedBy: "AI_SYSTEM",
          evaluatedAt: new Date()
        },
        create: {
          candidateId: candidateId,
          phaseId: phaseId,
          score: scoringResults.final_score,
          feedback: scoringResults as any,
          evaluatedBy: "AI_SYSTEM",
          evaluatedAt: new Date()
        }
      });

      console.log(`Scoring completed for candidate ${candidateId}. Score: ${scoringResults.final_score}`);

    } catch (error) {
      console.error(`Error scoring candidate ${candidateId}:`, error);
      throw error;
    }
  }

  async scoreAllCandidates(projectId: number): Promise<{ success: boolean; scored_count: number; failed_count: number }> {
    try {
      console.log(`Starting bulk scoring for project ${projectId}`);

      // Get all candidates in the project
      const candidates = await this.findByProject(projectId);
      
      let scoredCount = 0;
      let failedCount = 0;

      for (const candidate of candidates) {
        try {
          if (candidate.currentPhaseId) {
            await this.scoreCandidate(candidate.id, candidate.currentPhaseId);
            scoredCount++;
          }
        } catch (error) {
          console.error(`Failed to score candidate ${candidate.id}:`, error);
          failedCount++;
        }
      }

      console.log(`Bulk scoring completed: ${scoredCount} successful, ${failedCount} failed`);

      return {
        success: true,
        scored_count: scoredCount,
        failed_count: failedCount
      };

    } catch (error) {
      console.error("Error in bulk scoring:", error);
      throw error;
    }
  }

  private parseJobRequirements(jobOffer: any): JobRequirements {
    try {
      // Try to parse requiredSkills as JSON first
      let requirements: JobRequirements = {};
      
      if (jobOffer.requiredSkills) {
        try {
          requirements = JSON.parse(jobOffer.requiredSkills);
        } catch {
          // If not JSON, treat as comma-separated skills
          requirements.required_skills = jobOffer.requiredSkills.split(',').map((s: string) => s.trim());
        }
      }

      // Add other job offer fields
      requirements.required_education = jobOffer.experienceLevel;
      requirements.location_requirements = jobOffer.location;
      
      return requirements;
    } catch (error) {
      console.error("Error parsing job requirements:", error);
      return {};
    }
  }

  private mapToResponseDTO(candidate: any): CandidateResponseDTO {
    return {
      id: candidate.id,
      name: candidate.name,
      email: candidate.email,
      phone: candidate.phone,
      city: candidate.city,
      parsedProfile: candidate.parsedProfile,
      cvFileUrl: candidate.cvFileUrl,
      currentPhaseId: candidate.currentPhaseId,
      currentStatus: candidate.currentStatus,
      applicationDate: candidate.applicationDate,
      updatedAt: candidate.updatedAt
    };
  }
}
