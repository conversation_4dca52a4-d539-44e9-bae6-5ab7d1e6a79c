
import prisma from "@/config/prisma";
import { CreateProjectDTO, UpdateProjectDTO } from "@/dto/project.dto";
import { IProjectService } from "../interfaces/IProjectService";
import { provideSingleton } from "@/utils/provideSingleton";
import { TYPES } from "@/config/types";

interface ServiceError extends Error {
  code?: string;
  details?: any;
  service?: string;
  method?: string;
}

@provideSingleton(TYPES.IProjectService)
export class ProjectService implements IProjectService {
  async create(data: CreateProjectDTO) {
    return prisma.project.create({ data });
  }

  async findAll() {
    return await prisma.project.findMany();
  }

  async findById(id: number) {
    return prisma.project.findUnique({ where: { id } });
  }

  async update(id: number, data: UpdateProjectDTO) {
    return prisma.project.update({ where: { id }, data });
  }

  async delete(id: number) {
    return prisma.project.delete({ where: { id } });
  }
}