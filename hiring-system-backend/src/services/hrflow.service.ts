
import Hrflow from 'hrflow';
import { ParsedProfile } from "@/dto/candidate.dto";
import { appConfig } from "@/config/config";
import { IHRFlowService } from "@/interfaces/IHRFlowService";
import { provideSingleton } from "@/utils/provideSingleton";
import { TYPES } from "@/config/types";

@provideSingleton(TYPES.IHRFlowService)
export class HRFlowService implements IHRFlowService {
  private client: any;

  constructor() {
    this.client = new Hrflow({
      api_secret: appConfig.hrflowApiKey,
      api_user: appConfig.hrflowUserEmail
    });
  }

  async parseCV(fileBuffer: Buffer, filename: string): Promise<ParsedProfile | null> {
    try {
      console.log(`Starting CV parsing for file: ${filename}`);

      // Create a source if it doesn't exist
      const sourceKey = appConfig.hrflowSourceKey || "default_source";

      // Upload and parse the CV - Updated for hrflow v0.1.2
      const response = await this.client.profile.parsing.addFile(
        sourceKey,
        fileBuffer,
        filename,
        {
          sync_parsing: true,
          webhook_parsing_sending: false
        }
      );

      if (!response || !response.data) {
        console.error("Invalid response from HRFlow API");
        return null;
      }

      console.log("CV parsing successful");
      return this.transformHRFlowResponse(response.data);

    } catch (error) {
      console.error("Error parsing CV with HRFlow:", error);
      // Return a fallback parsed profile for testing
      return this.createFallbackProfile(filename);
    }
  }

  private getContentType(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/pdf';
    }
  }

  private transformHRFlowResponse(hrflowData: any): ParsedProfile {
    try {
      const profile = hrflowData.profile || {};
      
      return {
        personal_info: {
          name: profile.info?.full_name || "",
          email: profile.info?.email || "",
          phone: profile.info?.phone || "",
          location: profile.info?.location?.text || "",
          summary: profile.summary || ""
        },
        education: (profile.educations || []).map((edu: any) => ({
          degree: edu.title || "",
          institution: edu.school || "",
          graduationYear: edu.end_date ? new Date(edu.end_date).getFullYear() : undefined,
          field: edu.description || "",
          gpa: edu.grade || ""
        })),
        experience: (profile.experiences || []).map((exp: any) => ({
          title: exp.title || "",
          company: exp.company || "",
          startDate: exp.start_date || "",
          endDate: exp.end_date || "",
          description: exp.description || "",
          location: exp.location?.text || "",
          duration: this.calculateDuration(exp.start_date, exp.end_date)
        })),
        skills: (profile.skills || []).map((skill: any) => skill.name || skill).filter(Boolean),
        languages: (profile.languages || []).map((lang: any) => lang.name || lang).filter(Boolean),
        certifications: (profile.certifications || []).map((cert: any) => cert.name || cert).filter(Boolean)
      };
    } catch (error) {
      console.error("Error transforming HRFlow response:", error);
      return {
        personal_info: {},
        education: [],
        experience: [],
        skills: [],
        languages: [],
        certifications: []
      };
    }
  }

  private calculateDuration(startDate: string, endDate: string): string {
    if (!startDate) return "";
    
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffMonths / 12);
    
    if (diffYears > 0) {
      const remainingMonths = diffMonths % 12;
      return remainingMonths > 0 ? `${diffYears}y ${remainingMonths}m` : `${diffYears}y`;
    } else if (diffMonths > 0) {
      return `${diffMonths}m`;
    } else {
      return `${diffDays}d`;
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      // Test the connection by making a simple API call
      const response = await this.client.source.list();
      return response && (response.code === 200 || response.status === 200);
    } catch (error) {
      console.error("HRFlow connection validation failed:", error);
      return false;
    }
  }

  private createFallbackProfile(filename: string): ParsedProfile {
    console.log(`Creating fallback profile for ${filename}`);

    // Extract name from filename if possible
    const nameFromFile = filename.replace(/\.(pdf|doc|docx)$/i, '').replace(/[_-]/g, ' ');

    return {
      personal_info: {
        name: nameFromFile || "Unknown Candidate",
        email: `candidate_${Date.now()}@example.com`,
        summary: "Profile parsed from CV upload - manual review required"
      },
      education: [],
      experience: [],
      skills: [],
      languages: ["English"],
      certifications: []
    };
  }
}
