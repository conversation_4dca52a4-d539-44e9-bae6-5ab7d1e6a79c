import { injectable } from 'inversify';
import * as crypto from 'crypto';
import * as qs from 'qs';
import { PrismaClient } from '@prisma/client';
import { appConfig } from '@/config/config';
import { IWebhookService } from '@/interfaces/IWebhookService';
import { 
  HRFlowWebhookPayload, 
  WebhookEventCreate, 
  WebhookEventResponse, 
  WebhookProcessingResult,
  HRFlowProfile 
} from '@/dto/webhook.dto';
import { ParsedProfile } from '@/dto/candidate.dto';
import { provideSingleton } from '@/utils/provideSingleton';
import { TYPES } from '@/config/types';

@provideSingleton(TYPES.IWebhookService)
export class WebhookService implements IWebhookService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  verifyWebhookSignature(payload: string, signature: string): boolean {
    // For development, skip signature verification
    console.log('Skipping webhook signature verification for development');
    return true;

    // Production code would be:
    /*
    if (!appConfig.hrflowWebhookSecret) {
      console.warn('No webhook secret configured, skipping signature verification');
      return true;
    }

    if (!signature) {
      console.warn('No signature provided, skipping verification');
      return true;
    }

    const expectedSignature = crypto
      .createHmac('sha256', appConfig.hrflowWebhookSecret)
      .update(payload)
      .digest('hex');

    // Try different signature formats
    const expectedFormats = [
      `sha256=${expectedSignature}`,
      expectedSignature
    ];

    for (const expected of expectedFormats) {
      if (crypto.timingSafeEqual(Buffer.from(expected), Buffer.from(signature))) {
        console.log('Webhook signature verified successfully');
        return true;
      }
    }

    console.error(`Webhook signature verification failed. Expected one of: ${expectedFormats}, got: ${signature}`);
    return false;
    */
  }

  parseWebhookPayload(formData: Record<string, any>): HRFlowWebhookPayload {
    try {
      console.log('Parsing webhook payload');
      
      // Extract fields with flexible handling for different webhook formats
      const webhookType = formData.type || '';
      const origin = formData.origin || 'hrflow_ui';
      const message = formData.message || `HRFlow webhook: ${webhookType}`;
      
      // Handle profile data
      let profile = formData.profile || {};
      
      // If profile is still a string, try to parse as JSON
      if (typeof profile === 'string') {
        try {
          profile = profile !== '{}' ? JSON.parse(profile) : {};
        } catch (error) {
          console.warn('Failed to parse profile JSON, using empty object');
          profile = {};
        }
      }

      return {
        type: webhookType,
        origin,
        message,
        profile,
        team_name: formData.team_name
      };
    } catch (error) {
      console.error('Webhook payload parsing error:', error);
      throw new Error(`Invalid webhook payload: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async processWebhook(payload: HRFlowWebhookPayload, rawPayload: Record<string, any>): Promise<WebhookProcessingResult> {
    console.log(`Processing webhook: type=${payload.type}, origin=${payload.origin}`);
    
    // Create webhook event record
    const webhookEvent = await this.createWebhookEvent(payload, rawPayload);

    try {
      if (payload.type === 'profile.parsing.success' || 
          payload.type === 'profile.searching.success' || 
          payload.type === 'profile.storing.success') {
        await this.handleProfileSuccess(payload);
        
        // Update webhook event as processed
        await this.prisma.webhookEvent.update({
          where: { id: webhookEvent.id },
          data: {
            processed: true,
            processedAt: new Date()
          }
        });

        return {
          success: true,
          message: `Webhook processed successfully: ${payload.type}`,
          webhookEventId: webhookEvent.id
        };
      } else if (payload.type.startsWith('profile.')) {
        // Handle other profile-related events
        console.log(`Received profile event: ${payload.type} - logging but not processing`);
        
        await this.prisma.webhookEvent.update({
          where: { id: webhookEvent.id },
          data: {
            processed: true,
            processedAt: new Date()
          }
        });

        return {
          success: true,
          message: `Profile event logged: ${payload.type}`,
          webhookEventId: webhookEvent.id
        };
      } else {
        // Log unknown webhook types but don't fail
        console.warn(`Unknown webhook type: ${payload.type}`);
        
        await this.prisma.webhookEvent.update({
          where: { id: webhookEvent.id },
          data: {
            processed: true,
            processedAt: new Date(),
            processingError: `Unknown webhook type: ${payload.type}`
          }
        });

        return {
          success: true,
          message: `Unknown webhook type logged: ${payload.type}`,
          webhookEventId: webhookEvent.id
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Webhook processing error:', errorMessage);
      
      // Update webhook event with error
      await this.prisma.webhookEvent.update({
        where: { id: webhookEvent.id },
        data: {
          processingError: errorMessage
        }
      });

      return {
        success: false,
        message: 'Webhook processing failed',
        webhookEventId: webhookEvent.id,
        error: errorMessage
      };
    }
  }

  async createWebhookEvent(payload: HRFlowWebhookPayload, rawPayload: Record<string, any>): Promise<WebhookEventResponse> {
    const profileData = typeof payload.profile === 'object' ? payload.profile : {};
    
    // Extract profile and source keys
    const profileKey = profileData.key || null;
    const sourceKey = profileData.source?.key || null;

    const webhookEvent = await this.prisma.webhookEvent.create({
      data: {
        eventType: payload.type,
        origin: payload.origin,
        message: payload.message,
        profileKey,
        sourceKey,
        rawPayload
      }
    });

    return {
      id: webhookEvent.id,
      eventType: webhookEvent.eventType,
      origin: webhookEvent.origin,
      message: webhookEvent.message || '',
      profileKey: webhookEvent.profileKey || undefined,
      sourceKey: webhookEvent.sourceKey || undefined,
      processed: webhookEvent.processed,
      processingError: webhookEvent.processingError || undefined,
      receivedAt: webhookEvent.receivedAt,
      processedAt: webhookEvent.processedAt || undefined
    };
  }

  async handleProfileSuccess(payload: HRFlowWebhookPayload): Promise<void> {
    const profileData = typeof payload.profile === 'object' ? payload.profile as HRFlowProfile : {};
    const profileKey = profileData.key;

    console.log(`Handling profile success for profile key: ${profileKey}`);

    // Handle different webhook types
    if (payload.type === 'profile.storing.success') {
      // UI webhooks don't include profile data, just log the event
      console.log(`Profile storing success webhook received (type: ${payload.type})`);
      console.log('This is likely from HRFlow UI - no profile data to process');
      return;
    }

    if (!profileKey) {
      console.warn(`No profile key found in webhook payload for type: ${payload.type}`);
      console.log('Profile data:', profileData);
      return;
    }

    // TODO: Implement candidate creation/update logic
    // This would involve:
    // 1. Check if candidate already exists with this HRFlow profile key
    // 2. If not, create new candidate record
    // 3. Transform HRFlow profile data to our candidate format
    // 4. Update candidate status and processing information
    
    console.log(`Profile success handling completed for profile key: ${profileKey}`);
  }
}
