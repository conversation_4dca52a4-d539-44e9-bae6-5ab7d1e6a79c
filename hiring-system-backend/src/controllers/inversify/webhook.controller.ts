import { Request, Response } from 'express';
import { controller, httpPost, request, response } from 'inversify-express-utils';
import { inject } from 'inversify';
import * as qs from 'qs';
import { IWebhookService } from '@/interfaces/IWebhookService';
import { TYPES } from '@/config/types';

@controller('/webhooks')
export class WebhookController {
  constructor(
    @inject(TYPES.IWebhookService) private webhookService: IWebhookService
  ) {}

  @httpPost('/hrflow')
  public async hrflowWebhook(
    @request() req: Request,
    @response() res: Response
  ): Promise<void> {
    try {
      console.log('Webhook received - Headers:', req.headers);
      console.log('Webhook received - Body:', req.body);

      // For now, skip signature verification since we don't have raw body
      // In production, you would need to implement proper raw body capture
      console.log('Skipping signature verification for development');

      // Use the parsed body directly
      const parsedData = req.body;
      console.log('Parsed webhook data keys:', Object.keys(parsedData));

      // Parse the webhook payload
      const payload = this.webhookService.parseWebhookPayload(parsedData);

      // Validate required fields
      if (!payload.type) {
        res.status(400).json({
          success: false,
          message: 'Missing required field: type',
          webhookEventId: 0,
          error: 'Invalid payload structure'
        });
        return;
      }

      console.log(`Processing webhook: type=${payload.type}, origin=${payload.origin}`);

      // Process the webhook
      const result = await this.webhookService.processWebhook(payload, parsedData);

      if (!result.success) {
        res.status(500);
      }

      res.json(result);

    } catch (error) {
      console.error('Webhook processing error:', error);
      res.status(500).json({
        success: false,
        message: 'Webhook processing failed',
        webhookEventId: 0,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  @httpPost('/debug')
  public async debugWebhook(
    @request() req: Request,
    @response() res: Response
  ): Promise<void> {
    try {
      res.json({
        headers: req.headers,
        body: req.body,
        parsedData: req.body
      });
    } catch (error) {
      console.error('Debug webhook error:', error);
      res.status(500).json({
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  @httpPost('/test')
  public async testWebhook(
    @request() req: Request,
    @response() res: Response
  ): Promise<void> {
    res.json({
      message: 'Webhook endpoint is active',
      timestamp: new Date().toISOString()
    });
  }
}
