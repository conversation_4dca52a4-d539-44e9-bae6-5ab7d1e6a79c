import * as express from "express";
import { interfaces, controller, httpGet, httpPost, httpPut, httpDelete, request, response, requestParam, requestBody } from "inversify-express-utils";
import { inject } from "inversify";
import { TYPES } from "@/config/types";
import { IProjectService } from "@/interfaces/IProjectService";
import { ICandidateService } from "@/interfaces/ICandidateService";
import { CreateProjectDTO, UpdateProjectDTO, ProjectResponseDTO } from "@/dto/project.dto";
import { CandidateResponseDTO } from "@/dto/candidate.dto";


@controller("/projects")
export class ProjectController implements interfaces.Controller {

    constructor(
        @inject(TYPES.IProjectService) private projectService: IProjectService,
        @inject(TYPES.ICandidateService) private candidateService: ICandidateService
    ) {}

    @httpGet("/")
    private async getAllProjects(@request() req: express.Request, @response() res: express.Response): Promise<void> {
        try {
            const projects = await this.projectService.findAll();
            const projectsResponse = projects.map(project => ({
                ...project,
                startDate: project.startDate?.toISOString() || null,
                endDate: project.endDate?.toISOString() || null,
                createdAt: project.createdAt.toISOString(),
                updatedAt: project.updatedAt.toISOString()
            }));
            res.json(projectsResponse);
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.getAllProjects - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to fetch projects" });
        }
    }

    @httpGet("/:id")
    private async getProject(@requestParam("id") id: string, @response() res: express.Response): Promise<void> {
        try {
            const projectId = parseInt(id);
            const project = await this.projectService.findById(projectId);
            
            if (!project) {
                res.status(404).json({ error: "Project not found" });
                return;
            }
            
            const projectResponse = {
                ...project,
                startDate: project.startDate?.toISOString() || null,
                endDate: project.endDate?.toISOString() || null,
                createdAt: project.createdAt.toISOString(),
                updatedAt: project.updatedAt.toISOString()
            };
            
            res.json(projectResponse);
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.getProject - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to fetch project" });
        }
    }

    @httpPost("/")
    private async createProject(@requestBody() projectData: CreateProjectDTO, @response() res: express.Response): Promise<void> {
        try {
            const project = await this.projectService.create(projectData);
            const projectResponse = {
                ...project,
                startDate: project.startDate?.toISOString() || null,
                endDate: project.endDate?.toISOString() || null,
                createdAt: project.createdAt.toISOString(),
                updatedAt: project.updatedAt.toISOString()
            };
            res.status(201).json(projectResponse);
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.createProject - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to create project" });
        }
    }

    @httpPut("/:id")
    private async updateProject(@requestParam("id") id: string, @requestBody() projectData: UpdateProjectDTO, @response() res: express.Response): Promise<void> {
        try {
            const projectId = parseInt(id);
            const project = await this.projectService.update(projectId, projectData);
            
            if (!project) {
                res.status(404).json({ error: "Project not found" });
                return;
            }
            
            const projectResponse = {
                ...project,
                startDate: project.startDate?.toISOString() || null,
                endDate: project.endDate?.toISOString() || null,
                createdAt: project.createdAt.toISOString(),
                updatedAt: project.updatedAt.toISOString()
            };
            
            res.json(projectResponse);
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.updateProject - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to update project" });
        }
    }

    @httpDelete("/:id")
    private async deleteProject(@requestParam("id") id: string, @response() res: express.Response): Promise<void> {
        try {
            const projectId = parseInt(id);
            await this.projectService.delete(projectId);
            res.status(204).send();
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.deleteProject - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to delete project" });
        }
    }

    @httpGet("/:id/candidates")
    private async getProjectCandidates(@requestParam("id") id: string, @response() res: express.Response): Promise<void> {
        try {
            const projectId = parseInt(id);
            const candidates = await this.candidateService.findByProject(projectId);
            res.json(candidates);
        } catch (error) {
            console.error(`CONTROLLER ERROR ProjectController.getProjectCandidates - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to fetch project candidates" });
        }
    }
}
