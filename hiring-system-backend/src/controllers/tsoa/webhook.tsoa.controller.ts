import { 
  Controller, 
  Post, 
  Route, 
  Tags, 
  Request, 
  Response,
  SuccessResponse,
  Example
} from 'tsoa';
import { inject } from 'inversify';
import * as qs from 'qs';
import { Request as ExpressRequest } from 'express';
import { IWebhookService } from '@/interfaces/IWebhookService';
import { WebhookProcessingResult } from '@/dto/webhook.dto';
import { TYPES } from '@/config/types';

@Route('webhooks')
@Tags('Webhooks')
export class WebhookController extends Controller {
  constructor(
    @inject(TYPES.IWebhookService) private webhookService: IWebhookService
  ) {
    super();
  }

  /**
   * HRFlow webhook endpoint for profile parsing updates
   * 
   * Receives webhook notifications when profiles are processed by HRFlow.
   * The webhook payload is sent as form-encoded data.
   */
  @Post('hrflow')
  @SuccessResponse(200, 'Webhook processed successfully')
  @Response(400, 'Invalid webhook payload')
  @Response(401, 'Invalid webhook signature')
  @Response(500, 'Webhook processing failed')
  @Example<WebhookProcessingResult>({
    success: true,
    message: 'Webhook processed successfully: profile.parsing.success',
    webhookEventId: 123
  })
  public async hrflowWebhook(
    @Request() request: ExpressRequest
  ): Promise<WebhookProcessingResult> {
    try {
      console.log('Webhook received - Headers:', request.headers);
      
      // Get raw body
      const rawBody = (request as any).rawBody || '';
      console.log('Raw body length:', rawBody.length);
      
      // Parse form data using qs
      const parsedData = qs.parse(rawBody);
      console.log('Parsed webhook data keys:', Object.keys(parsedData));
      
      // Verify webhook signature
      const signature = request.headers['x-signature'] as string || 
                       request.headers['http-hrflow-signature'] as string || '';
      
      if (!this.webhookService.verifyWebhookSignature(rawBody, signature)) {
        this.setStatus(401);
        return {
          success: false,
          message: 'Invalid webhook signature',
          webhookEventId: 0,
          error: 'Signature verification failed'
        };
      }

      // Parse the webhook payload
      const payload = this.webhookService.parseWebhookPayload(parsedData);
      
      // Validate required fields
      if (!payload.type) {
        this.setStatus(400);
        return {
          success: false,
          message: 'Missing required field: type',
          webhookEventId: 0,
          error: 'Invalid payload structure'
        };
      }

      console.log(`Processing webhook: type=${payload.type}, origin=${payload.origin}`);

      // Process the webhook
      const result = await this.webhookService.processWebhook(payload, parsedData);
      
      if (!result.success) {
        this.setStatus(500);
      }
      
      return result;

    } catch (error) {
      console.error('Webhook processing error:', error);
      this.setStatus(500);
      
      return {
        success: false,
        message: 'Webhook processing failed',
        webhookEventId: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Debug webhook endpoint for testing
   * 
   * Returns the parsed webhook data for debugging purposes.
   */
  @Post('debug')
  @SuccessResponse(200, 'Debug data returned')
  @Example<Record<string, any>>({
    type: 'profile.parsing.success',
    origin: 'api',
    message: 'profile parsing succeed',
    profile: {
      key: 'profile_key_123',
      source: { key: 'source_key_456' }
    }
  })
  public async debugWebhook(
    @Request() request: ExpressRequest
  ): Promise<Record<string, any>> {
    try {
      const rawBody = (request as any).rawBody || '';
      const parsed = qs.parse(rawBody);
      
      return {
        headers: request.headers,
        rawBodyLength: rawBody.length,
        parsedData: parsed
      };
    } catch (error) {
      console.error('Debug webhook error:', error);
      this.setStatus(500);
      return {
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test webhook endpoint
   * 
   * Simple endpoint to verify webhook configuration is working.
   */
  @Post('test')
  @SuccessResponse(200, 'Test successful')
  @Example<{ message: string; timestamp: string }>({
    message: 'Webhook endpoint is active',
    timestamp: '2024-01-01T00:00:00.000Z'
  })
  public async testWebhook(): Promise<{ message: string; timestamp: string }> {
    return {
      message: 'Webhook endpoint is active',
      timestamp: new Date().toISOString()
    };
  }
}
