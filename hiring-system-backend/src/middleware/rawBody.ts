import { Request, Response, NextFunction } from 'express';
import rawBodyParser from 'raw-body';

/**
 * Middleware to capture raw body for webhook signature verification
 * This should be applied before the body parsing middleware for webhook routes
 */
export function rawBodyMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Only capture raw body for webhook routes that need signature verification
  if (req.path === '/webhooks/hrflow' || req.path === '/webhooks/debug') {
    rawBodyParser(req, {
      length: req.headers['content-length'],
      limit: '1mb',
      encoding: 'utf8'
    })
    .then((body: string) => {
      (req as any).rawBody = body;
      next();
    })
    .catch((err: Error) => {
      console.error('Raw body parsing error:', err);
      next(err);
    });
  } else {
    next();
  }
}
