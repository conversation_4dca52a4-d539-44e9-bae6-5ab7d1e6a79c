import { Request, Response, NextFunction } from 'express';
import { ValidateError } from 'tsoa';

export interface CustomError extends Error {
  status?: number;
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * Global error handler middleware for Express
 */
export function errorHandler(
  err: CustomError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const requestId = req.headers['x-request-id'] || 'unknown';
  console.error(`ERROR ${req.method} ${req.originalUrl} - ${err.name}: ${err.message} [${requestId}]`);

  if (isDevelopment) {
    console.error(`  IP: ${req.ip}`);
    console.error(`  User-Agent: ${req.get('User-Agent')}`);

    if (Object.keys(req.body).length > 0) {
      console.error(`  Body: ${JSON.stringify(req.body)}`);
    }
    if (Object.keys(req.query).length > 0) {
      console.error(`  Query: ${JSON.stringify(req.query)}`);
    }
    if (Object.keys(req.params).length > 0) {
      console.error(`  Params: ${JSON.stringify(req.params)}`);
    }
  }
  
  // Handle TSOA validation errors
  if (err instanceof ValidateError) {
    console.error(`VALIDATION ${req.method} ${req.originalUrl} - ${Object.keys(err.fields).join(', ')} [${requestId}]`);

    if (isDevelopment) {
      console.error(`  Fields: ${JSON.stringify(err.fields)}`);
    }

    const errorResponse = {
      error: 'Validation Failed',
      message: 'Request validation failed',
      details: err.fields,
      ...(isDevelopment && {
        stack: err.stack,
        timestamp: new Date().toISOString(),
        requestId: requestId
      })
    };

    res.status(422).json(errorResponse);
    return;
  }

  // Handle different error types
  const status = err.status || err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  if (isDevelopment) {
    console.error(`  Status: ${status}`);
    console.error(`  Code: ${err.code || 'N/A'}`);

    if (err.details) {
      console.error(`  Details: ${JSON.stringify(err.details)}`);
    }

    if (err.stack) {
      console.error(`  Stack: ${err.stack}`);
    }
  }

  // Prepare error response
  const errorResponse: any = {
    error: err.name || 'Error',
    message: message,
    status: status,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method
  };

  // Add development-specific details
  if (isDevelopment) {
    errorResponse.stack = err.stack;
    errorResponse.details = err.details;
    errorResponse.code = err.code;
    errorResponse.requestId = req.headers['x-request-id'] || 'unknown';
    errorResponse.requestBody = req.body;
    errorResponse.requestQuery = req.query;
    errorResponse.requestParams = req.params;
  }

  res.status(status).json(errorResponse);
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(req: Request, res: Response): void {
  const requestId = req.headers['x-request-id'] || 'unknown';
  console.warn(`404 ${req.method} ${req.originalUrl} - Not Found [${requestId}]`);

  const errorResponse = {
    error: 'Not Found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    status: 404,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    availableRoutes: [
      'GET /projects',
      'POST /projects',
      'GET /projects/{id}',
      'PUT /projects/{id}',
      'DELETE /projects/{id}',
      'GET /projects/{id}/candidates',
      'POST /projects/{id}/score-all',
      'GET /candidates',
      'POST /candidates',
      'GET /candidates/{id}',
      'PUT /candidates/{id}',
      'DELETE /candidates/{id}',
      'POST /candidates/{candidateId}/score/{phaseId}',
      'GET /api-docs'
    ]
  };

  res.status(404).json(errorResponse);
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
